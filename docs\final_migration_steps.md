# Django 迁移最终步骤指南

## 当前状态

✅ **已完成**:
- Django项目结构创建
- 数据模型完全迁移
- API视图完全迁移
- 基础模板迁移
- 插件集成逻辑更新
- 迁移脚本和工具创建

⚠️ **待执行**: 数据库迁移和最终测试

## 立即执行的步骤

### 步骤1: 执行Django数据库迁移

在 `plugins/builtin/dashboard_web/` 目录下执行：

```powershell
# 方法1: 使用批处理脚本（推荐）
quick_migrate.bat

# 方法2: 手动执行命令
python manage.py makemigrations dashboard_app
python manage.py migrate
python manage.py check
```

### 步骤2: 测试Django环境

```powershell
# 简单测试
python simple_test.py

# 完整测试
python test_migration.py
```

### 步骤3: 测试Django服务器

```powershell
# 启动开发服务器
python manage.py runserver 127.0.0.1:5001

# 在浏览器中访问
# http://127.0.0.1:5001/
# http://127.0.0.1:5001/health/
```

### 步骤4: 迁移现有数据（可选）

如果有现有的Flask数据库：

```powershell
# 试运行（不实际迁移）
python manage.py migrate_flask_data --dry-run

# 实际迁移
python manage.py migrate_flask_data
```

### 步骤5: 测试RunSim GUI集成

1. 启动RunSim GUI
2. 在工具菜单中找到"项目仪表板"
3. 点击打开仪表板
4. 验证所有功能正常

## 故障排除

### 问题1: Django导入失败
```
ModuleNotFoundError: No module named 'django'
```
**解决方案**: 确保Django已安装
```powershell
pip install Django>=4.2.0
```

### 问题2: 数据库迁移失败
```
django.db.utils.OperationalError: no such table
```
**解决方案**: 重新执行迁移
```powershell
python manage.py migrate --run-syncdb
```

### 问题3: 静态文件404
```
GET /static/css/dashboard.css 404
```
**解决方案**: 收集静态文件
```powershell
python manage.py collectstatic --noinput
```

### 问题4: 端口占用
```
OSError: [Errno 98] Address already in use
```
**解决方案**: 更改端口或停止占用进程
```powershell
# 查找占用进程
netstat -ano | findstr :5001

# 停止进程
taskkill /PID <进程ID> /F
```

### 问题5: 插件启动失败
**解决方案**: 检查插件日志，确保Django环境正确

## 验证清单

### 基础功能验证
- [ ] Django服务器正常启动
- [ ] 主页面正常加载
- [ ] API接口正常响应
- [ ] 数据库连接正常

### 页面功能验证
- [ ] 仪表板页面显示正常
- [ ] 统计数据正确显示
- [ ] 图表正常渲染
- [ ] 用例管理页面正常

### API功能验证
- [ ] `/api/health/` 返回正常
- [ ] `/api/dashboard/statistics/` 返回数据
- [ ] `/api/testplan/cases/` 返回用例列表
- [ ] `/api/bugs/` 返回BUG列表

### 集成功能验证
- [ ] RunSim GUI插件正常启动
- [ ] 浏览器正常打开仪表板
- [ ] 用例状态更新正常
- [ ] 数据导入/导出正常

## 性能基准

### 预期性能指标
- 页面加载时间: < 3秒
- API响应时间: < 1秒
- 数据库查询时间: < 500ms
- 内存使用: < 200MB

### 性能测试命令
```powershell
# 测试API响应时间
curl -w "@curl-format.txt" -o /dev/null -s "http://127.0.0.1:5001/api/health/"

# 测试页面加载时间
curl -w "@curl-format.txt" -o /dev/null -s "http://127.0.0.1:5001/"
```

## 回滚方案

如果Django迁移出现问题，可以回滚到Flask版本：

1. **备份Django文件**:
   ```powershell
   move dashboard_project django_backup
   move dashboard_app django_backup
   ```

2. **恢复Flask文件**:
   确保原有的Flask文件（app.py, models/, routes/等）仍然存在

3. **更新插件配置**:
   在 `dashboard_plugin.py` 中设置 `self.use_django = False`

4. **重启RunSim GUI**

## 后续优化

### 短期优化（1周内）
- [ ] 完善错误处理
- [ ] 优化数据库查询
- [ ] 添加缓存机制
- [ ] 完善日志记录

### 中期优化（1个月内）
- [ ] 添加用户认证
- [ ] 实现数据备份
- [ ] 添加性能监控
- [ ] 优化前端界面

### 长期优化（3个月内）
- [ ] 微服务架构
- [ ] 分布式部署
- [ ] 高可用配置
- [ ] 自动化测试

## 支持和维护

### 日志文件位置
- Django日志: `plugins/builtin/dashboard_web/logs/django.log`
- 插件日志: RunSim GUI日志文件

### 配置文件位置
- Django设置: `plugins/builtin/dashboard_web/dashboard_project/settings.py`
- 插件配置: `plugins/builtin/dashboard_plugin.py`

### 数据库文件位置
- SQLite数据库: 根据配置文件中的路径

## 联系支持

如果遇到问题：
1. 检查日志文件
2. 参考故障排除部分
3. 查看Django官方文档
4. 联系开发团队

---

**重要提醒**: 在生产环境中使用前，请务必完成所有验证步骤并进行充分测试。
