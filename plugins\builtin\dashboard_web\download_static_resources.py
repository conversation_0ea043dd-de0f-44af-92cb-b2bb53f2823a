#!/usr/bin/env python3
"""
静态资源下载脚本

该脚本用于下载RunSim GUI仪表板所需的静态资源文件，包括：
- Bootstrap CSS和JavaScript
- jQuery库
- Chart.js图表库
"""

import os
import sys
import urllib.request
import urllib.error
from datetime import datetime

class StaticResourceDownloader:
    """静态资源下载器"""
    
    def __init__(self):
        self.static_dir = os.path.join(os.path.dirname(__file__), 'static')
        self.css_dir = os.path.join(self.static_dir, 'css')
        self.js_dir = os.path.join(self.static_dir, 'js')
        
        # 资源URL配置
        self.resources = {
            'css/bootstrap.min.css': {
                'url': 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
                'description': 'Bootstrap CSS框架',
                'size': '~160KB'
            },
            'js/bootstrap.min.js': {
                'url': 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js',
                'description': 'Bootstrap JavaScript',
                'size': '~80KB'
            },
            'js/jquery.min.js': {
                'url': 'https://code.jquery.com/jquery-3.6.0.min.js',
                'description': 'jQuery库',
                'size': '~90KB'
            },
            'js/chart.min.js': {
                'url': 'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js',
                'description': 'Chart.js图表库',
                'size': '~200KB'
            }
        }
        
        self.download_results = {}
    
    def download_all_resources(self):
        """下载所有静态资源"""
        print("=" * 60)
        print("RunSim GUI 仪表板 - 静态资源下载")
        print("=" * 60)
        print(f"下载开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        try:
            # 1. 创建目录
            print("1. 创建静态资源目录...")
            self._create_directories()
            
            # 2. 下载资源
            print("\n2. 下载静态资源...")
            self._download_resources()
            
            # 3. 验证下载
            print("\n3. 验证下载结果...")
            self._verify_downloads()
            
            # 4. 生成报告
            print("\n4. 生成下载报告...")
            self._generate_report()
            
            print("\n✅ 静态资源下载完成！")
            return True
            
        except Exception as e:
            print(f"\n❌ 静态资源下载失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [self.static_dir, self.css_dir, self.js_dir]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                print(f"  ✓ 创建目录: {directory}")
            else:
                print(f"  ✓ 目录已存在: {directory}")
    
    def _download_resources(self):
        """下载所有资源"""
        for file_path, config in self.resources.items():
            print(f"\n  下载 {config['description']}...")
            print(f"    文件: {file_path}")
            print(f"    大小: {config['size']}")
            print(f"    URL: {config['url']}")
            
            success = self._download_file(file_path, config['url'])
            
            self.download_results[file_path] = {
                'success': success,
                'url': config['url'],
                'description': config['description']
            }
            
            if success:
                print(f"    ✓ 下载成功")
            else:
                print(f"    ❌ 下载失败")
    
    def _download_file(self, file_path, url):
        """下载单个文件"""
        try:
            full_path = os.path.join(self.static_dir, file_path)
            
            # 检查文件是否已存在
            if os.path.exists(full_path):
                file_size = os.path.getsize(full_path)
                if file_size > 1000:  # 文件大于1KB，认为是有效文件
                    print(f"    ⚠️ 文件已存在，跳过下载 ({file_size} 字节)")
                    return True
            
            # 下载文件
            print(f"    正在下载...")
            
            # 设置请求头，模拟浏览器
            req = urllib.request.Request(url)
            req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            
            with urllib.request.urlopen(req, timeout=30) as response:
                content = response.read()
                
                # 保存文件
                with open(full_path, 'wb') as f:
                    f.write(content)
                
                file_size = len(content)
                print(f"    下载完成: {file_size} 字节")
                
                return True
                
        except urllib.error.URLError as e:
            print(f"    网络错误: {e}")
            return False
        except urllib.error.HTTPError as e:
            print(f"    HTTP错误: {e.code} - {e.reason}")
            return False
        except Exception as e:
            print(f"    下载错误: {e}")
            return False
    
    def _verify_downloads(self):
        """验证下载结果"""
        print("  验证下载的文件...")
        
        total_files = len(self.resources)
        successful_downloads = 0
        total_size = 0
        
        for file_path, result in self.download_results.items():
            full_path = os.path.join(self.static_dir, file_path)
            
            if os.path.exists(full_path):
                file_size = os.path.getsize(full_path)
                total_size += file_size
                
                if file_size > 1000:  # 文件大于1KB
                    successful_downloads += 1
                    print(f"    ✓ {file_path} ({file_size:,} 字节)")
                else:
                    print(f"    ❌ {file_path} (文件过小: {file_size} 字节)")
            else:
                print(f"    ❌ {file_path} (文件不存在)")
        
        print(f"\n  验证结果:")
        print(f"    成功下载: {successful_downloads}/{total_files}")
        print(f"    总文件大小: {total_size:,} 字节 ({total_size/1024/1024:.1f} MB)")
        print(f"    成功率: {successful_downloads/total_files*100:.1f}%")
    
    def _generate_report(self):
        """生成下载报告"""
        try:
            report = {
                'download_time': datetime.now().isoformat(),
                'results': self.download_results,
                'summary': self._generate_summary()
            }
            
            # 保存报告
            import json

# 配置UTF-8输出
import sys
import io
if hasattr(sys.stdout, 'buffer'):
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
if hasattr(sys.stderr, 'buffer'):
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

            report_file = f'static_resources_download_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"  下载报告已保存: {report_file}")
            
            # 显示摘要
            self._display_summary()
            
        except Exception as e:
            print(f"  ❌ 生成下载报告失败: {e}")
    
    def _generate_summary(self):
        """生成下载摘要"""
        summary = {
            'total_files': len(self.resources),
            'successful_downloads': 0,
            'failed_downloads': 0,
            'total_size': 0
        }
        
        for file_path, result in self.download_results.items():
            if result['success']:
                summary['successful_downloads'] += 1
                
                # 计算文件大小
                full_path = os.path.join(self.static_dir, file_path)
                if os.path.exists(full_path):
                    summary['total_size'] += os.path.getsize(full_path)
            else:
                summary['failed_downloads'] += 1
        
        return summary
    
    def _display_summary(self):
        """显示下载摘要"""
        print("\n" + "=" * 50)
        print("下载摘要")
        print("=" * 50)
        
        summary = self._generate_summary()
        
        print(f"总文件数: {summary['total_files']}")
        print(f"成功下载: {summary['successful_downloads']}")
        print(f"下载失败: {summary['failed_downloads']}")
        print(f"成功率: {summary['successful_downloads']/summary['total_files']*100:.1f}%")
        print(f"总大小: {summary['total_size']:,} 字节 ({summary['total_size']/1024/1024:.1f} MB)")
        
        print("\n文件状态:")
        for file_path, result in self.download_results.items():
            status = "✅" if result['success'] else "❌"
            print(f"  {status} {file_path} - {result['description']}")
        
        if summary['failed_downloads'] > 0:
            print("\n⚠️ 部分文件下载失败，可能的原因:")
            print("  1. 网络连接问题")
            print("  2. CDN服务不可用")
            print("  3. 防火墙或代理限制")
            print("\n建议:")
            print("  1. 检查网络连接")
            print("  2. 稍后重试")
            print("  3. 手动下载缺失文件")

def create_fallback_files():
    """创建备用文件（简化版本）"""
    print("\n创建备用文件...")
    
    static_dir = os.path.join(os.path.dirname(__file__), 'static')
    
    # 创建简化的CSS文件
    css_content = """
/* 简化版Bootstrap样式 */
.container { max-width: 1200px; margin: 0 auto; padding: 0 15px; }
.row { display: flex; flex-wrap: wrap; margin: 0 -15px; }
.col, .col-md-3, .col-md-6, .col-md-12 { padding: 0 15px; flex: 1; }
.col-md-3 { flex: 0 0 25%; }
.col-md-6 { flex: 0 0 50%; }
.col-md-12 { flex: 0 0 100%; }
.card { border: 1px solid #ddd; border-radius: 4px; margin-bottom: 20px; }
.card-header { background: #f8f9fa; padding: 10px 15px; border-bottom: 1px solid #ddd; }
.card-body { padding: 15px; }
.btn { padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; }
.btn-primary { background: #007bff; color: white; }
.btn-success { background: #28a745; color: white; }
.navbar { background: #343a40; padding: 10px 0; }
.navbar-brand { color: white; font-weight: bold; }
.nav-link { color: #ccc; text-decoration: none; margin: 0 10px; }
.alert { padding: 15px; margin: 20px 0; border-radius: 4px; }
.alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
"""
    
    css_file = os.path.join(static_dir, 'css', 'bootstrap.min.css')
    with open(css_file, 'w', encoding='utf-8') as f:
        f.write(css_content)
    print(f"  ✓ 创建简化CSS: {css_file}")
    
    # 创建简化的JavaScript文件
    js_content = """
// 简化版jQuery和Bootstrap功能
window.$ = window.jQuery = function(selector) {
    if (typeof selector === 'string') {
        return {
            ready: function(fn) { 
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', fn);
                } else {
                    fn();
                }
            },
            get: function(url, callback) {
                fetch(url).then(r => r.json()).then(callback);
            },
            text: function(text) {
                const el = document.querySelector(selector);
                if (el) el.textContent = text;
            }
        };
    } else if (typeof selector === 'function') {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', selector);
        } else {
            selector();
        }
    }
    return {};
};

// Chart.js简化版
window.Chart = function(ctx, config) {
    console.log('Chart.js fallback - 图表功能需要完整的Chart.js库');
    return { update: function() {}, destroy: function() {} };
};
"""
    
    # 保存jQuery
    jquery_file = os.path.join(static_dir, 'js', 'jquery.min.js')
    with open(jquery_file, 'w', encoding='utf-8') as f:
        f.write(js_content)
    print(f"  ✓ 创建简化jQuery: {jquery_file}")
    
    # 保存Bootstrap JS
    bootstrap_file = os.path.join(static_dir, 'js', 'bootstrap.min.js')
    with open(bootstrap_file, 'w', encoding='utf-8') as f:
        f.write("// Bootstrap JS fallback\nconsole.log('Bootstrap JS fallback loaded');")
    print(f"  ✓ 创建简化Bootstrap JS: {bootstrap_file}")
    
    # 保存Chart.js
    chart_file = os.path.join(static_dir, 'js', 'chart.min.js')
    with open(chart_file, 'w', encoding='utf-8') as f:
        f.write(js_content.split('// Chart.js简化版')[1])
    print(f"  ✓ 创建简化Chart.js: {chart_file}")

def main():
    """主函数"""
    downloader = StaticResourceDownloader()
    
    print("选择下载方式:")
    print("1. 从CDN下载完整文件（推荐）")
    print("2. 创建简化版备用文件")
    print("3. 两种方式都尝试")
    
    try:
        choice = input("\n请选择 (1/2/3，默认为3): ").strip() or "3"
        
        if choice in ['1', '3']:
            success = downloader.download_all_resources()
            if not success and choice == '3':
                print("\nCDN下载失败，创建备用文件...")
                create_fallback_files()
        elif choice == '2':
            create_fallback_files()
        else:
            print("无效选择，执行默认操作...")
            success = downloader.download_all_resources()
            if not success:
                create_fallback_files()
        
        print("\n🎉 静态资源处理完成！")
        return 0
        
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        return 1
    except Exception as e:
        print(f"\n操作失败: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
