#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复模板文件中的Flask语法为Django语法
"""

import os
import re
import sys
import io
from pathlib import Path

# 配置UTF-8输出
if hasattr(sys.stdout, 'buffer'):
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
if hasattr(sys.stderr, 'buffer'):
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

def fix_template_file(file_path):
    """修复单个模板文件"""
    print(f"修复模板文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 1. 替换 url_for('static', filename='...') 为 {% static '...' %}
    content = re.sub(
        r"url_for\('static',\s*filename='([^']+)'\)",
        r"{% static '\1' %}",
        content
    )
    
    # 2. 替换 url_for('route_name') 为 {% url 'app_name:route_name' %}
    url_mappings = {
        "url_for('index')": "{% url 'dashboard_app:dashboard' %}",
        "url_for('dashboard')": "{% url 'dashboard_app:dashboard' %}",
        "url_for('testplan')": "{% url 'dashboard_app:testplan' %}",
        "url_for('bug')": "{% url 'dashboard_app:bug' %}",
        "url_for('health')": "{% url 'dashboard_app:health' %}",
    }
    
    for flask_url, django_url in url_mappings.items():
        content = content.replace(flask_url, django_url)
    
    # 3. 添加 {% load static %} 如果文件中使用了static标签
    if "{% static" in content and "{% load static %}" not in content:
        # 在第一行添加 {% load static %}
        lines = content.split('\n')
        if lines and not lines[0].strip().startswith('{% load'):
            lines.insert(0, '{% load static %}')
            content = '\n'.join(lines)
    
    # 4. 替换模板继承语法（如果需要）
    content = content.replace('{% extends "base.html" %}', '{% extends "base_django.html" %}')
    
    # 5. 修复其他可能的Flask语法
    # 替换 {{ config.XXX }} 为适当的Django语法
    content = re.sub(r'\{\{\s*config\.([^}]+)\s*\}\}', r'{{ settings.\1 }}', content)
    
    # 如果内容有变化，写回文件
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✅ 已修复")
        return True
    else:
        print(f"  ⚪ 无需修复")
        return False

def main():
    """主函数"""
    print("修复Django模板语法")
    print("=" * 40)
    
    # 获取模板目录
    current_dir = Path(__file__).parent.absolute()
    templates_dir = current_dir / 'templates'
    
    if not templates_dir.exists():
        print(f"❌ 模板目录不存在: {templates_dir}")
        return False
    
    # 查找所有HTML模板文件
    template_files = list(templates_dir.glob('*.html'))
    
    if not template_files:
        print("❌ 未找到模板文件")
        return False
    
    print(f"找到 {len(template_files)} 个模板文件")
    
    fixed_count = 0
    for template_file in template_files:
        if fix_template_file(template_file):
            fixed_count += 1
    
    print(f"\n修复完成: {fixed_count}/{len(template_files)} 个文件已修复")
    
    # 创建Django版本的主要模板文件
    create_django_templates()
    
    return True

def create_django_templates():
    """创建Django版本的主要模板文件"""
    print("\n创建Django版本的模板文件...")
    
    current_dir = Path(__file__).parent.absolute()
    templates_dir = current_dir / 'templates'
    
    # 创建Django版本的base.html
    base_django_content = '''{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}RunSim 项目仪表板{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- 自定义样式 -->
    <link href="{% static 'css/dashboard.css' %}" rel="stylesheet">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'dashboard_app:dashboard' %}">
                <i class="fas fa-chart-line me-2"></i>RunSim 仪表板
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'dashboard_app:dashboard' %}">
                            <i class="fas fa-tachometer-alt me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'dashboard_app:testplan' %}">
                            <i class="fas fa-list-check me-1"></i>用例管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'dashboard_app:bug' %}">
                            <i class="fas fa-bug me-1"></i>BUG管理
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text">
                            <i class="fas fa-clock me-1"></i>
                            <span id="current-time"></span>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <small>&copy; 2024 RunSim GUI Dashboard. All rights reserved.</small>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- 仪表板核心脚本 -->
    <script src="{% static 'js/dashboard.js' %}"></script>

    <!-- Error Handler -->
    <script src="{% static 'js/error-handler.js' %}"></script>

    <!-- Common JavaScript -->
    <script>
        // 更新当前时间
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            $('#current-time').text(timeString);
        }

        // 每秒更新时间
        setInterval(updateCurrentTime, 1000);
        updateCurrentTime();

        // 通用错误处理
        function showError(message) {
            console.error(message);
        }

        // 通用成功提示
        function showSuccess(message) {
            console.log(message);
        }

        // AJAX错误处理
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            if (settings.url && !settings.url.includes('chatgpt.io') && !settings.url.includes('api.chatgpt.io')) {
                showError('请求失败: ' + thrownError);
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>'''
    
    # 写入base.html文件（覆盖原有的Flask版本）
    base_file = templates_dir / 'base.html'
    with open(base_file, 'w', encoding='utf-8') as f:
        f.write(base_django_content)
    print(f"✅ 已创建/更新: {base_file}")

if __name__ == '__main__':
    success = main()
    if success:
        print("\n🎉 模板修复完成！")
        print("\n下一步:")
        print("1. 运行: python fix_migration.py")
        print("2. 运行: python manage.py runserver 127.0.0.1:5001")
    else:
        print("\n❌ 模板修复失败")
