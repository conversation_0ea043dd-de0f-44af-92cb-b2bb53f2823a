"""
RunSim GUI 仪表板插件

该插件为RunSim GUI提供项目管理仪表板功能，包括：
- 用例管理和状态跟踪
- BUG记录和统计分析
- 项目进度可视化展示
- Excel文件导入导出

技术实现：
- 使用Flask作为Web服务器
- 前端使用Bootstrap + Chart.js
- 数据存储使用SQLite
- 通过浏览器访问仪表板界面
"""

import os
import sys
import threading
import webbrowser
import socket
import time
import logging
from PyQt5.QtWidgets import QAction, QMessageBox
from PyQt5.QtCore import QTimer
from plugins.base import PluginBase

# 配置日志 - 支持Unicode字符
import sys
import io

# 创建支持UTF-8的日志处理器
class UTF8StreamHandler(logging.StreamHandler):
    def __init__(self, stream=None):
        if stream is None:
            stream = sys.stderr
        # 确保流支持UTF-8编码
        if hasattr(stream, 'buffer'):
            stream = io.TextIOWrapper(stream.buffer, encoding='utf-8', errors='replace')
        super().__init__(stream)

# 配置日志格式和处理器
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s %(message)s',
    handlers=[UTF8StreamHandler()]
)
logger = logging.getLogger(__name__)

class DashboardPlugin(PluginBase):
    """仪表板插件主类"""

    @property
    def name(self):
        return "项目仪表板"

    @property
    def version(self):
        return "1.0.0"

    @property
    def description(self):
        return "项目管理仪表板，包括用例管理、BUG记录和进度展示"

    def __init__(self):
        """初始化插件"""
        super().__init__()
        self.server_thread = None
        self.server_port = 5001
        self.main_window = None
        self.flask_app = None
        self._preloaded_modules = {}  # 预加载的模块
        self._dashboard_path = None   # dashboard路径
        self._original_cwd = None     # 原始工作目录
        self._paths_to_add = []       # 需要添加的Python路径
        self.server_running = False

        # 状态检查定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._check_server_status)

    def initialize(self, main_window):
        """
        初始化插件

        Args:
            main_window: RunSim GUI主窗口实例
        """
        try:
            self.main_window = main_window
            logger.info(f"开始初始化插件: {self.name}")

            # 创建菜单项
            self._create_menu_action()

            # 启动Web服务器
            self._start_web_server()

            # 启动状态检查定时器
            self.status_timer.start(10000)  # 每10秒检查一次

            logger.info(f"插件初始化成功: {self.name}")

        except Exception as e:
            logger.error(f"初始化插件 {self.name} 失败: {str(e)}")
            self._show_error_message(f"插件初始化失败: {str(e)}")

    def _create_menu_action(self):
        """创建菜单项"""
        try:
            self.menu_action = QAction(self.name, self.main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self.open_dashboard)

            # 添加到工具菜单
            if hasattr(self.main_window, 'tools_menu'):
                self.main_window.tools_menu.addSeparator()
                self.main_window.tools_menu.addAction(self.menu_action)
                logger.info("菜单项创建成功")
            else:
                logger.warning("主窗口没有tools_menu属性")

        except Exception as e:
            logger.error(f"创建菜单项失败: {str(e)}")

    def _start_web_server(self):
        """启动Web服务器"""
        if self.server_thread is None or not self.server_thread.is_alive():
            # 固定使用5001端口，如果被占用则尝试停止现有服务器
            self.server_port = 5001

            # 检查端口是否被占用
            if not self._is_port_available(self.server_port):
                logger.warning(f"端口 {self.server_port} 被占用，尝试查找其他可用端口")
                self.server_port = self._find_available_port()

            # 预先检查依赖
            if not self._check_dependencies():
                logger.error("依赖检查失败，无法启动Web服务器")
                return

            # 预先设置Python路径和预导入模块（在主线程中）
            if not self._prepare_modules():
                logger.error("模块准备失败，无法启动Web服务器")
                return

            # 启动服务器线程
            self.server_thread = threading.Thread(
                target=self._run_server,
                daemon=True,
                name="DashboardWebServer"
            )
            self.server_thread.start()

            # 等待服务器启动
            self._wait_for_server_start()

            logger.info(f"Web服务器启动成功，端口: {self.server_port}")
        else:
            logger.info("Web服务器已在运行")

    def _is_port_available(self, port):
        """
        检查端口是否可用

        Args:
            port: 端口号

        Returns:
            bool: 端口是否可用
        """
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.bind(('127.0.0.1', port))
                return True
        except OSError:
            return False

    def _find_available_port(self, start_port=5001, max_attempts=10):
        """
        查找可用端口

        Args:
            start_port: 起始端口号
            max_attempts: 最大尝试次数

        Returns:
            int: 可用的端口号
        """
        for port in range(start_port, start_port + max_attempts):
            if self._is_port_available(port):
                return port

        # 如果没有找到可用端口，使用默认端口
        logger.warning(f"未找到可用端口，使用默认端口: {start_port}")
        return start_port

    def _check_dependencies(self):
        """检查依赖是否满足"""
        try:
            # 优先检查Django
            try:
                import django
                logger.info(f"Django版本: {django.VERSION}")
                self.use_django = True
            except ImportError:
                # 回退到Flask
                try:
                    import flask
                    logger.info(f"Flask版本: {flask.__version__}")
                    self.use_django = False
                except ImportError:
                    logger.error("既没有Django也没有Flask，无法启动Web服务器")
                    return False

            # 检查dashboard_web目录
            dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard_web')
            if not os.path.exists(dashboard_path):
                logger.error(f"dashboard_web目录不存在: {dashboard_path}")
                return False

            # 检查关键文件
            if self.use_django:
                required_files = [
                    os.path.join(dashboard_path, 'manage.py'),
                    os.path.join(dashboard_path, 'dashboard_project', 'settings.py'),
                    os.path.join(dashboard_path, 'dashboard_app', 'models.py'),
                ]
            else:
                required_files = [
                    os.path.join(dashboard_path, 'app.py'),
                    os.path.join(dashboard_path, 'models', 'database.py'),
                ]

            for file_path in required_files:
                if not os.path.exists(file_path):
                    logger.error(f"关键文件不存在: {file_path}")
                    return False

            framework = "Django" if self.use_django else "Flask"
            logger.info(f"依赖检查通过，使用 {framework} 框架")
            return True

        except Exception as e:
            logger.error(f"依赖检查异常: {e}")
            return False

    def _prepare_modules(self):
        """
        预先设置Python路径和导入模块（在主线程中）

        Returns:
            bool: 是否准备成功
        """
        try:
            import sys
            import os

            # 获取路径
            dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard_web')
            plugin_dir = os.path.dirname(__file__)

            # 确保dashboard_web目录存在
            if not os.path.exists(dashboard_path):
                logger.error(f"Dashboard目录不存在: {dashboard_path}")
                return False

            logger.info("开始准备模块...")
            logger.info(f"Dashboard路径: {dashboard_path}")
            logger.info(f"插件路径: {plugin_dir}")

            # 设置Python路径
            paths_to_add = [
                dashboard_path,  # 最高优先级，包含app.py
                os.path.join(dashboard_path, 'models'),
                os.path.join(dashboard_path, 'utils'),
                os.path.join(dashboard_path, 'routes'),
                plugin_dir
            ]

            # 清理旧路径
            for path in paths_to_add:
                while path in sys.path:
                    sys.path.remove(path)

            # 按优先级添加路径
            for path in reversed(paths_to_add):
                if os.path.exists(path):
                    sys.path.insert(0, path)
                    logger.info(f"添加Python路径: {path}")

            # 保存原始工作目录到环境变量，供配置模块使用
            original_cwd = os.getcwd()
            if 'RUNSIM_ORIGINAL_CWD' not in os.environ:
                os.environ['RUNSIM_ORIGINAL_CWD'] = original_cwd
                logger.info(f"保存原始工作目录: {original_cwd}")

            # 切换到dashboard_web目录
            os.chdir(dashboard_path)
            logger.info(f"工作目录切换到: {os.getcwd()}")

            try:
                # 预先导入关键模块（在主线程中）
                logger.info("预先导入关键模块...")

                # 导入数据库模块
                try:
                    # 使用importlib直接从指定路径导入，避免与根目录models包冲突
                    import importlib.util
                    database_file = os.path.join(dashboard_path, 'models', 'database.py')

                    spec = importlib.util.spec_from_file_location("dashboard_database", database_file)
                    database_module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(database_module)

                    # 获取需要的函数
                    get_db = getattr(database_module, 'get_db')
                    init_database = getattr(database_module, 'init_database')

                    logger.info("✅ models.database 预导入成功")

                    # 存储模块引用，供子线程使用
                    self._preloaded_modules = {
                        'models.database': database_module,
                        'get_db': get_db,
                        'init_database': init_database
                    }
                except Exception as e:
                    logger.error(f"❌ models.database 预导入失败: {e}")
                    return False

                # 导入其他关键模块
                try:
                    # 使用importlib导入testplan模块
                    testplan_file = os.path.join(dashboard_path, 'models', 'testplan.py')

                    spec = importlib.util.spec_from_file_location("dashboard_testplan", testplan_file)
                    testplan_module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(testplan_module)

                    # 获取需要的类
                    TestCaseManager = getattr(testplan_module, 'TestCaseManager')

                    self._preloaded_modules['models.testplan'] = testplan_module
                    self._preloaded_modules['TestCaseManager'] = TestCaseManager
                    logger.info("✅ models.testplan 预导入成功")
                except Exception as e:
                    logger.warning(f"⚠️ models.testplan 预导入失败: {e}")

                try:
                    # 使用importlib导入excel_parser模块
                    excel_parser_file = os.path.join(dashboard_path, 'utils', 'excel_parser.py')

                    if os.path.exists(excel_parser_file):
                        spec = importlib.util.spec_from_file_location("dashboard_excel_parser", excel_parser_file)
                        excel_parser_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(excel_parser_module)

                        # 获取需要的类
                        TestPlanParser = getattr(excel_parser_module, 'TestPlanParser')

                        self._preloaded_modules['utils.excel_parser'] = excel_parser_module
                        self._preloaded_modules['TestPlanParser'] = TestPlanParser
                        logger.info("✅ utils.excel_parser 预导入成功")
                    else:
                        logger.warning(f"⚠️ excel_parser.py文件不存在: {excel_parser_file}")
                except Exception as e:
                    logger.warning(f"⚠️ utils.excel_parser 预导入失败: {e}")

                # 导入Flask应用模块
                try:
                    # 使用importlib导入app模块
                    app_file = os.path.join(dashboard_path, 'app.py')

                    spec = importlib.util.spec_from_file_location("dashboard_app", app_file)
                    app_module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(app_module)

                    # 获取需要的函数
                    create_app = getattr(app_module, 'create_app')

                    self._preloaded_modules['app'] = app_module
                    self._preloaded_modules['create_app'] = create_app
                    logger.info("✅ app 模块预导入成功")
                except Exception as e:
                    logger.error(f"❌ app 模块预导入失败: {e}")
                    return False

                # 存储路径信息供子线程使用
                self._dashboard_path = dashboard_path
                self._original_cwd = original_cwd
                self._paths_to_add = paths_to_add

                logger.info("✅ 模块准备完成")
                return True

            finally:
                # 恢复工作目录
                os.chdir(original_cwd)

        except Exception as e:
            logger.error(f"模块准备失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def _run_server(self):
        """运行Web服务器（Django或Flask）"""
        try:
            if hasattr(self, 'use_django') and self.use_django:
                self._run_django_server()
            else:
                self._run_flask_server()
        except Exception as e:
            self.server_running = False
            logger.error(f"启动Web服务器失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            self._show_error_message(f"Web服务器启动失败: {str(e)}")

    def _run_django_server(self):
        """运行Django服务器"""
        try:
            import sys
            import os

            logger.info("开始启动Django服务器...")

            # 获取dashboard_web目录
            dashboard_path = os.path.join(os.path.dirname(__file__), 'dashboard_web')

            # 保存原始工作目录
            original_cwd = os.getcwd()
            if 'RUNSIM_ORIGINAL_CWD' not in os.environ:
                os.environ['RUNSIM_ORIGINAL_CWD'] = original_cwd
                logger.info(f"保存原始工作目录: {original_cwd}")

            # 切换到Django项目目录
            os.chdir(dashboard_path)
            logger.info(f"工作目录切换到: {os.getcwd()}")

            # 添加Django项目路径到Python路径
            if dashboard_path not in sys.path:
                sys.path.insert(0, dashboard_path)

            try:
                # 设置Django环境
                os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dashboard_project.settings')

                import django
                django.setup()
                logger.info("✅ Django环境设置成功")

                # 执行数据库迁移（如果需要）
                try:
                    from django.core.management import execute_from_command_line
                    from io import StringIO
                    import sys as sys_backup

                    # 捕获输出以避免干扰
                    old_stdout = sys_backup.stdout
                    sys_backup.stdout = StringIO()

                    try:
                        execute_from_command_line(['manage.py', 'migrate', '--run-syncdb'])
                        logger.info("✅ 数据库迁移检查完成")
                    finally:
                        sys_backup.stdout = old_stdout

                except Exception as e:
                    logger.warning(f"⚠️ 数据库迁移检查失败: {e}")

                # 设置服务器运行状态
                self.server_running = True

                # 启动Django开发服务器
                logger.info(f"启动Django服务器，地址: http://127.0.0.1:{self.server_port}")

                from django.core.management import execute_from_command_line
                execute_from_command_line([
                    'manage.py',
                    'runserver',
                    f'127.0.0.1:{self.server_port}',
                    '--noreload',  # 禁用自动重载
                    '--nothreading'  # 使用单线程模式
                ])

            finally:
                # 恢复工作目录
                os.chdir(original_cwd)
                logger.info(f"工作目录恢复到: {os.getcwd()}")

        except Exception as e:
            self.server_running = False
            logger.error(f"启动Django服务器失败: {str(e)}")
            raise

    def _run_flask_server(self):
        """运行Flask服务器"""
        try:
            # 使用预加载的模块和路径信息
            import sys
            import os

            logger.info("开始启动Flask服务器...")

            # 恢复Python路径设置
            if hasattr(self, '_paths_to_add'):
                for path in self._paths_to_add:
                    if path not in sys.path:
                        sys.path.insert(0, path)
                        logger.debug(f"恢复Python路径: {path}")

            # 保存原始工作目录到环境变量（如果还没有保存）
            original_cwd = os.getcwd()
            if 'RUNSIM_ORIGINAL_CWD' not in os.environ:
                os.environ['RUNSIM_ORIGINAL_CWD'] = original_cwd
                logger.info(f"保存原始工作目录: {original_cwd}")

            # 切换工作目录
            if hasattr(self, '_dashboard_path'):
                os.chdir(self._dashboard_path)
                logger.info(f"工作目录切换到: {os.getcwd()}")

            try:
                # 使用预加载的模块
                if hasattr(self, '_preloaded_modules') and 'create_app' in self._preloaded_modules:
                    logger.info("使用预加载的模块创建Flask应用...")
                    create_app = self._preloaded_modules['create_app']
                else:
                    # 备用方案：重新导入
                    logger.warning("预加载模块不可用，尝试重新导入...")
                    import importlib.util
                    app_file = os.path.join(self._dashboard_path, 'app.py')
                    spec = importlib.util.spec_from_file_location("dashboard_app_backup", app_file)
                    app_module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(app_module)
                    create_app = getattr(app_module, 'create_app')

                # 创建应用配置
                try:
                    # 尝试导入配置模块
                    import importlib.util
                    config_file = os.path.join(self._dashboard_path, 'config.py')
                    if os.path.exists(config_file):
                        spec = importlib.util.spec_from_file_location("dashboard_config", config_file)
                        config_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(config_module)

                        # 获取配置
                        get_config = getattr(config_module, 'get_config')
                        ensure_directories = getattr(config_module, 'ensure_directories')

                        # 确保目录存在
                        ensure_directories()

                        # 获取生产环境配置
                        config_obj = get_config('production')
                        config = {
                            'DEBUG': False,
                            'TESTING': False,
                            'PLUGIN_MODE': True,  # 标识这是插件模式启动
                            'SECRET_KEY': config_obj.SECRET_KEY,
                            'DATABASE_PATH': config_obj.DATABASE_PATH,
                            'UPLOAD_FOLDER': config_obj.UPLOAD_FOLDER,
                            'EXPORT_FOLDER': config_obj.EXPORT_FOLDER,
                            'MAX_CONTENT_LENGTH': config_obj.MAX_CONTENT_LENGTH
                        }
                        logger.info("✅ 使用配置文件中的设置")
                    else:
                        # 备用配置
                        config = {
                            'DEBUG': False,
                            'TESTING': False,
                            'PLUGIN_MODE': True  # 标识这是插件模式启动
                        }
                        logger.warning("⚠️ 配置文件不存在，使用默认配置")
                except Exception as e:
                    logger.warning(f"⚠️ 加载配置失败，使用默认配置: {e}")
                    config = {
                        'DEBUG': False,
                        'TESTING': False,
                        'PLUGIN_MODE': True  # 标识这是插件模式启动
                    }

                logger.info("创建Flask应用...")
                self.flask_app = create_app(config)
                logger.info("✅ Flask应用创建成功")

                # 设置服务器运行状态
                self.server_running = True

                # 运行服务器
                logger.info(f"启动Flask服务器，地址: http://127.0.0.1:{self.server_port}")

                self.flask_app.run(
                    host='127.0.0.1',
                    port=self.server_port,
                    debug=False,
                    use_reloader=False,
                    threaded=True
                )

            finally:
                # 恢复工作目录
                os.chdir(original_cwd)
                logger.info(f"工作目录恢复到: {os.getcwd()}")

        except Exception as e:
            self.server_running = False
            logger.error(f"启动Web服务器失败: {str(e)}")
            # 记录详细错误信息
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            self._show_error_message(f"Web服务器启动失败: {str(e)}")

    def _wait_for_server_start(self, timeout=10):
        """
        等待服务器启动

        Args:
            timeout: 超时时间（秒）
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                    sock.settimeout(1)
                    result = sock.connect_ex(('127.0.0.1', self.server_port))
                    if result == 0:
                        self.server_running = True
                        return True
            except:
                pass
            time.sleep(0.5)

        logger.warning("服务器启动超时")
        return False

    def _check_server_status(self):
        """检查服务器状态"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(('127.0.0.1', self.server_port))
                if result != 0:
                    self.server_running = False
                    logger.warning("检测到服务器已停止，尝试重启")
                    self._start_web_server()
        except Exception as e:
            logger.error(f"检查服务器状态失败: {str(e)}")

    def open_dashboard(self):
        """打开仪表板页面"""
        try:
            if not self.server_running:
                self._show_error_message("Web服务器未运行，请稍后重试")
                return

            url = f"http://127.0.0.1:{self.server_port}"
            logger.info(f"打开仪表板页面: {url}")

            # 使用默认浏览器打开页面
            webbrowser.open(url)

        except Exception as e:
            logger.error(f"打开仪表板页面失败: {str(e)}")
            self._show_error_message(f"无法打开仪表板页面: {str(e)}")

    def update_case_status(self, case_name, status, **kwargs):
        """
        更新用例状态

        Args:
            case_name: 用例名称
            status: 新状态
            **kwargs: 其他参数
        """
        try:
            if not self.server_running:
                logger.warning("服务器未运行，无法更新用例状态")
                return

            # 通过API更新用例状态
            import requests
            url = f"http://127.0.0.1:{self.server_port}/api/testplan/update_status"
            data = {
                'case_name': case_name,
                'status': status,
                'timestamp': time.time(),
                **kwargs
            }

            response = requests.post(url, json=data, timeout=5)
            if response.status_code == 200:
                logger.info(f"用例状态更新成功: {case_name} -> {status}")
            else:
                logger.warning(f"用例状态更新失败: {response.status_code}")

        except Exception as e:
            logger.error(f"更新用例状态失败: {str(e)}")

    def get_server_info(self):
        """
        获取服务器信息

        Returns:
            dict: 服务器信息
        """
        return {
            'running': self.server_running,
            'port': self.server_port,
            'url': f"http://127.0.0.1:{self.server_port}",
            'thread_alive': self.server_thread.is_alive() if self.server_thread else False
        }

    def _show_error_message(self, message):
        """显示错误消息"""
        if self.main_window:
            QMessageBox.warning(self.main_window, "仪表板插件错误", message)

    def cleanup(self):
        """清理插件资源"""
        try:
            logger.info("开始清理仪表板插件资源")

            # 停止状态检查定时器
            if self.status_timer.isActive():
                self.status_timer.stop()

            # 标记服务器停止
            self.server_running = False

            # Web服务器会随着主进程结束而自动结束
            logger.info("仪表板插件资源清理完成")

        except Exception as e:
            logger.error(f"清理插件资源失败: {str(e)}")

# 插件实例创建函数（供插件管理器使用）
def create_plugin():
    """创建插件实例"""
    return DashboardPlugin()
