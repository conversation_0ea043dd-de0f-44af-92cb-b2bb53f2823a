#!/usr/bin/env python
"""
重置数据库并重新导入数据
"""

import os
import sys
import requests
import json

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def reset_and_import():
    """重置数据库并重新导入数据"""
    print("🔄 重置数据库并重新导入数据")
    print("=" * 50)
    
    try:
        # 1. 清空数据库
        print("1. 清空数据库...")
        from models.database import get_db
        
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM test_cases")
            cursor.execute("DELETE FROM case_status_history")
            conn.commit()
            print("   ✅ 数据库清空完成")
        
        # 2. 检查服务器状态
        base_url = "http://127.0.0.1:5001"
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            if response.status_code == 200:
                print("   ✅ 服务器运行正常")
            else:
                print(f"   ❌ 服务器响应异常: {response.status_code}")
                return
        except Exception as e:
            print(f"   ❌ 无法连接到服务器: {e}")
            return
        
        # 3. 重新导入数据
        print("2. 重新导入TestPlan数据...")
        template_path = os.path.join(current_dir, '..', '..', '..', 'TestPlan_Template.xlsx')
        template_path = os.path.abspath(template_path)
        
        if not os.path.exists(template_path):
            print(f"   ❌ TestPlan模板文件不存在: {template_path}")
            return
        
        print(f"   📁 使用模板文件: {template_path}")
        
        with open(template_path, 'rb') as f:
            files = {
                'file': ('TestPlan_Template.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            }
            data = {
                'project_id': '1',
                'sheet_name': 'TP'
            }
            
            response = requests.post(
                f"{base_url}/api/testplan/import",
                files=files,
                data=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("   ✅ 导入成功!")
                    print(f"   📊 总解析: {result['data']['total_parsed']} 条")
                    print(f"   ✅ 有效用例: {result['data']['valid_cases']} 条")
                    print(f"   💾 成功导入: {result['data']['imported_count']} 条")
                    print(f"   ❌ 失败: {result['data']['failed_count']} 条")
                else:
                    print(f"   ❌ 导入失败: {result.get('error')}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                print(f"   📝 响应内容: {response.text[:200]}")
        
        # 4. 验证导入结果
        print("3. 验证导入结果...")
        response = requests.get(f"{base_url}/api/testplan/cases", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('data'):
                cases = data['data']['cases']
                print(f"   ✅ 数据库中现有 {len(cases)} 个用例")
                
                if cases:
                    print("   📋 前5个用例:")
                    for i, case in enumerate(cases[:5]):
                        print(f"      {i+1}. {case['case_name']} - {case.get('subsys_status', 'N/A')}")
                else:
                    print("   ⚠️ 数据库中没有用例数据")
            else:
                print(f"   ❌ 获取用例失败: {data.get('error')}")
        else:
            print(f"   ❌ 验证失败: {response.status_code}")
        
        print("\n🎉 重置和导入完成!")
        print(f"🌐 访问地址: {base_url}/testplan")
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        import traceback

# 配置UTF-8输出
import sys
import io
if hasattr(sys.stdout, 'buffer'):
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
if hasattr(sys.stderr, 'buffer'):
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

        traceback.print_exc()

if __name__ == '__main__':
    reset_and_import()
