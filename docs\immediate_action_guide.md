# Django 迁移立即执行指南

## 🚨 当前问题和解决方案

您遇到的两个主要问题已经有了完整的解决方案：

### 问题1: 数据库表已存在错误
```
sqlite3.OperationalError: table "projects" already exists
```

### 问题2: 模板语法错误
```
TemplateSyntaxError: Could not parse the remainder: '('index')' from 'url_for('index')'
```

## 🛠️ 立即执行的解决步骤

### 步骤1: 运行完整修复脚本（推荐）

在 `plugins/builtin/dashboard_web/` 目录下执行：

```powershell
# 方法1: 使用批处理脚本（最简单）
fix_all.bat

# 方法2: 直接运行Python脚本
python complete_fix.py
```

这个脚本会自动：
- ✅ 修复所有模板中的Flask语法为Django语法
- ✅ 解决数据库表冲突问题
- ✅ 执行Django系统检查
- ✅ 收集静态文件
- ✅ 测试服务器启动

### 步骤2: 手动执行（如果自动脚本失败）

如果自动脚本有问题，可以手动执行：

```powershell
# 1. 修复模板语法
python fix_templates.py

# 2. 修复数据库迁移冲突
python fix_migration.py

# 3. Django系统检查
python manage.py check

# 4. 收集静态文件
python manage.py collectstatic --noinput

# 5. 启动服务器测试
python manage.py runserver 127.0.0.1:5001
```

### 步骤3: 验证修复结果

1. **检查服务器启动**
   ```powershell
   python manage.py runserver 127.0.0.1:5001
   ```

2. **访问网页**
   - 打开浏览器访问: http://127.0.0.1:5001/
   - 检查页面是否正常加载
   - 验证没有模板语法错误

3. **测试API接口**
   - 访问: http://127.0.0.1:5001/api/health/
   - 应该返回JSON格式的健康检查信息

## 🔧 修复脚本说明

### complete_fix.py
- **功能**: 一键修复所有已知问题
- **包含**: 模板修复 + 数据库修复 + 系统检查 + 服务器测试
- **推荐**: 首选解决方案

### fix_templates.py
- **功能**: 修复模板中的Flask语法错误
- **修复内容**:
  - `url_for('static', filename='...')` → `{% static '...' %}`
  - `url_for('route')` → `{% url 'app:route' %}`
  - 添加 `{% load static %}` 标签
  - 更新模板继承语法

### fix_migration.py
- **功能**: 解决数据库表已存在的冲突
- **修复内容**:
  - 备份现有数据库
  - 修复表结构差异
  - 标记初始迁移为已应用
  - 创建Django迁移表

## 📋 预期结果

修复完成后，您应该看到：

1. **服务器正常启动**
   ```
   Django version 5.2.2, using settings 'dashboard_project.settings'
   Starting development server at http://127.0.0.1:5001/
   ```

2. **页面正常加载**
   - 仪表板页面显示正常
   - 没有模板语法错误
   - 静态文件（CSS/JS）正常加载

3. **API正常响应**
   ```json
   {
     "status": "healthy",
     "timestamp": "2024-...",
     "database": "connected",
     "version": "2.0.0-django"
   }
   ```

## 🚀 下一步操作

修复完成后：

1. **测试RunSim GUI集成**
   - 启动RunSim GUI
   - 点击工具菜单中的"项目仪表板"
   - 验证浏览器正常打开仪表板页面

2. **测试核心功能**
   - 仪表板数据显示
   - 用例管理功能
   - BUG管理功能
   - 数据导入/导出

3. **迁移现有数据（可选）**
   ```powershell
   # 如果有现有Flask数据库
   python manage.py migrate_flask_data
   ```

## ⚠️ 故障排除

### 如果修复脚本失败
1. 检查Django是否正确安装
2. 确保在正确的目录下执行
3. 查看错误信息并手动执行相应步骤

### 如果页面仍有错误
1. 检查浏览器控制台错误
2. 查看Django服务器日志
3. 确认静态文件路径正确

### 如果数据库问题持续
1. 备份现有数据库文件
2. 删除数据库文件重新创建
3. 重新执行迁移

## 📞 获取帮助

如果遇到问题：
1. 查看脚本输出的详细错误信息
2. 检查 `plugins/builtin/dashboard_web/logs/` 目录下的日志
3. 参考 `docs/django_deployment_guide.md` 中的故障排除部分

---

**立即行动**: 请现在就执行 `fix_all.bat` 或 `python complete_fix.py` 来解决所有问题！
