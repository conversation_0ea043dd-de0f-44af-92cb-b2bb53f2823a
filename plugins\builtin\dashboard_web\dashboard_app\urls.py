"""
Dashboard app URL configuration
"""
from django.urls import path, include
from . import views

app_name = 'dashboard_app'

urlpatterns = [
    # 主页面路由
    path('', views.dashboard_view, name='dashboard'),
    path('testplan/', views.testplan_view, name='testplan'),
    path('bug/', views.bug_view, name='bug'),
    path('health/', views.health_check, name='health'),
    
    # API路由
    path('api/', include([
        # 健康检查
        path('health/', views.api_health_check, name='api_health'),
        
        # 仪表板API
        path('dashboard/', include([
            path('statistics/', views.dashboard_statistics, name='dashboard_statistics'),
            path('case_pass_rate/', views.case_pass_rate, name='case_pass_rate'),
            path('bug_statistics/', views.bug_statistics, name='bug_statistics'),
        ])),
        
        # 测试计划API
        path('testplan/', include([
            path('cases/', views.testplan_cases, name='testplan_cases'),
            path('statistics/', views.testplan_statistics, name='testplan_statistics'),
            path('import/', views.testplan_import, name='testplan_import'),
            path('export/', views.testplan_export, name='testplan_export'),
            path('template/', views.testplan_template, name='testplan_template'),
            path('update_from_runsim/', views.update_from_runsim, name='update_from_runsim'),
            path('case/<int:case_id>/', views.get_case_detail, name='case_detail'),
        ])),
        
        # BUG管理API
        path('bugs/', views.bugs_api, name='bugs_api'),
        path('bugs/<int:bug_id>/', views.bug_detail_api, name='bug_detail_api'),
        path('bugs/statistics/', views.bugs_statistics, name='bugs_statistics'),
        
        # 系统配置API
        path('system/', include([
            path('config/', views.system_config, name='system_config'),
        ])),
    ])),
]
