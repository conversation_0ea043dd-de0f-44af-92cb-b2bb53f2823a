{% extends "base_django.html" %}

{% block title %}仪表板 - RunSim 项目仪表板{% endblock %}

{% block extra_css %}
<style>
    .metric-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .metric-value {
        font-size: 2.5rem;
        font-weight: bold;
        margin: 0;
    }
    .metric-label {
        font-size: 0.9rem;
        color: #6c757d;
        margin: 0;
    }
    .chart-card {
        height: 400px;
    }
    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
    }
    .refresh-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        display: none;
    }
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }
    .status-online {
        background-color: #28a745;
        animation: pulse 2s infinite;
    }
    .status-offline {
        background-color: #dc3545;
    }
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }
</style>
{% endblock %}

{% block content %}
<!-- 刷新指示器 -->
<div class="refresh-indicator">
    <div class="spinner-border spinner-border-sm text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
</div>

<!-- 页面标题 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">项目仪表板</h1>
                <p class="text-muted mb-0">实时监控项目进度和用例执行状态</p>
            </div>
            <div class="d-flex align-items-center">
                <span class="status-indicator status-online" id="connection-status"></span>
                <small class="text-muted me-3" id="connection-text">实时连接</small>
                <button class="btn btn-outline-primary btn-sm" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card metric-card text-white bg-primary h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <p class="metric-label text-white-50">总用例数</p>
                        <h2 class="metric-value" id="total-cases">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-list-check fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card metric-card text-white bg-success h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <p class="metric-label text-white-50">通过用例</p>
                        <h2 class="metric-value" id="passed-cases">-</h2>
                        <small class="text-white-50">通过率: <span id="pass-rate">-</span>%</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card metric-card text-white bg-info h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <p class="metric-label text-white-50">待处理用例</p>
                        <h2 class="metric-value" id="pending-cases">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-hourglass-half fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card metric-card text-white bg-warning h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <p class="metric-label text-white-50">进行中</p>
                        <h2 class="metric-value" id="running-cases">-</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x text-white-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分级别统计卡片 -->
<div class="row mb-4">
    <div class="col-lg-6 mb-3">
        <div class="card h-100">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">
                    <i class="fas fa-microchip me-2"></i>Subsys级别统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-success mb-1" id="subsys-passed">-</h4>
                            <small class="text-muted">通过</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-info mb-1" id="subsys-pending">-</h4>
                            <small class="text-muted">待处理</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h4 class="text-warning mb-1" id="subsys-running">-</h4>
                        <small class="text-muted">进行中</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-3">
        <div class="card h-100">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">
                    <i class="fas fa-sitemap me-2"></i>TOP级别统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-success mb-1" id="top-passed">-</h4>
                            <small class="text-muted">通过</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h4 class="text-info mb-1" id="top-pending">-</h4>
                            <small class="text-muted">待处理</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h4 class="text-warning mb-1" id="top-running">-</h4>
                        <small class="text-muted">进行中</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <!-- 用例状态分布 -->
    <div class="col-lg-6 mb-4">
        <div class="card chart-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>用例状态分布
                </h5>
                <small class="text-muted" id="status-chart-update">-</small>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目进度 -->
    <div class="col-lg-6 mb-4">
        <div class="card chart-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>项目进度
                </h5>
                <small class="text-muted" id="progress-chart-update">-</small>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="progressChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <a href="{% url 'dashboard_app:testplan' %}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-upload me-2"></i>导入TestPlan
                        </a>
                    </div>
                    <div class="col-md-4 mb-2">
                        <a href="{% url 'dashboard_app:bug' %}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-bug me-2"></i>BUG管理
                        </a>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-outline-info w-100" onclick="exportData()">
                            <i class="fas fa-download me-2"></i>导出数据
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Django仪表板JavaScript代码
let dashboardCharts = {};
let refreshInterval;

// 页面加载完成后初始化
$(document).ready(function() {
    initializeDashboard();
    startAutoRefresh();
});

function initializeDashboard() {
    loadDashboardData();
    initializeCharts();
}

function loadDashboardData() {
    showRefreshIndicator();
    
    // 加载统计数据
    $.get('/api/dashboard/statistics/')
        .done(function(response) {
            if (response.success) {
                updateStatistics(response.data);
            } else {
                showError('加载统计数据失败: ' + response.error);
            }
        })
        .fail(function() {
            showError('无法连接到服务器');
            updateConnectionStatus(false);
        })
        .always(function() {
            hideRefreshIndicator();
        });
}

function updateStatistics(data) {
    // 更新总体统计
    $('#total-cases').text(data.test_cases.total || 0);
    
    // 计算通过用例数和通过率
    const subsysPassed = data.test_cases.subsys.PASS || 0;
    const topPassed = data.test_cases.top.PASS || 0;
    const totalPassed = subsysPassed + topPassed;
    const passRate = data.test_cases.total > 0 ? 
        ((totalPassed / data.test_cases.total) * 100).toFixed(1) : 0;
    
    $('#passed-cases').text(totalPassed);
    $('#pass-rate').text(passRate);
    
    // 更新其他统计
    $('#pending-cases').text((data.test_cases.subsys.Pending || 0) + (data.test_cases.top.Pending || 0));
    $('#running-cases').text((data.test_cases.subsys['On-Going'] || 0) + (data.test_cases.top['On-Going'] || 0));
    
    // 更新分级别统计
    $('#subsys-passed').text(data.test_cases.subsys.PASS || 0);
    $('#subsys-pending').text(data.test_cases.subsys.Pending || 0);
    $('#subsys-running').text(data.test_cases.subsys['On-Going'] || 0);
    
    $('#top-passed').text(data.test_cases.top.PASS || 0);
    $('#top-pending').text(data.test_cases.top.Pending || 0);
    $('#top-running').text(data.test_cases.top['On-Going'] || 0);
    
    updateConnectionStatus(true);
}

function initializeCharts() {
    // 初始化状态分布图
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    dashboardCharts.statusChart = new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['通过', '待处理', '进行中', 'N/A'],
            datasets: [{
                data: [0, 0, 0, 0],
                backgroundColor: ['#28a745', '#17a2b8', '#ffc107', '#6c757d']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // 初始化进度图
    const progressCtx = document.getElementById('progressChart').getContext('2d');
    dashboardCharts.progressChart = new Chart(progressCtx, {
        type: 'bar',
        data: {
            labels: ['Subsys', 'TOP'],
            datasets: [{
                label: '通过',
                data: [0, 0],
                backgroundColor: '#28a745'
            }, {
                label: '待处理',
                data: [0, 0],
                backgroundColor: '#17a2b8'
            }, {
                label: '进行中',
                data: [0, 0],
                backgroundColor: '#ffc107'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    stacked: true
                },
                y: {
                    stacked: true
                }
            }
        }
    });
}

function refreshDashboard() {
    loadDashboardData();
}

function startAutoRefresh() {
    refreshInterval = setInterval(loadDashboardData, 30000); // 30秒刷新一次
}

function showRefreshIndicator() {
    $('.refresh-indicator').show();
}

function hideRefreshIndicator() {
    $('.refresh-indicator').hide();
}

function updateConnectionStatus(connected) {
    const statusElement = $('#connection-status');
    const textElement = $('#connection-text');
    
    if (connected) {
        statusElement.removeClass('status-offline').addClass('status-online');
        textElement.text('实时连接');
    } else {
        statusElement.removeClass('status-online').addClass('status-offline');
        textElement.text('连接断开');
    }
}

function exportData() {
    window.open('/api/testplan/export/', '_blank');
}

// 页面卸载时清理
$(window).on('beforeunload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
});
</script>
{% endblock %}
