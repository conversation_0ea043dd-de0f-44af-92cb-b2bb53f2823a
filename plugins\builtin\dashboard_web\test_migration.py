#!/usr/bin/env python
"""
Django迁移测试脚本

验证Django环境和迁移是否成功
"""

import os
import sys
import time
import subprocess
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

def run_command(cmd, description=""):
    """运行命令并返回结果"""
    print(f"\n{'='*50}")
    print(f"执行: {description or cmd}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            cwd=current_dir,
            timeout=60
        )
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description or cmd} 执行成功")
            return True
        else:
            print(f"❌ {description or cmd} 执行失败 (返回码: {result.returncode})")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description or cmd} 执行超时")
        return False
    except Exception as e:
        print(f"❌ {description or cmd} 执行异常: {e}")
        return False

def test_django_environment():
    """测试Django环境"""
    print("🔍 测试Django环境...")
    return run_command("python test_django.py", "Django环境测试")

def test_makemigrations():
    """测试生成迁移文件"""
    print("🔍 生成数据库迁移文件...")
    return run_command("python manage.py makemigrations dashboard_app", "生成迁移文件")

def test_migrate():
    """测试执行迁移"""
    print("🔍 执行数据库迁移...")
    return run_command("python manage.py migrate", "执行数据库迁移")

def test_check():
    """测试Django检查"""
    print("🔍 执行Django系统检查...")
    return run_command("python manage.py check", "Django系统检查")

def test_collectstatic():
    """测试收集静态文件"""
    print("🔍 收集静态文件...")
    return run_command("python manage.py collectstatic --noinput", "收集静态文件")

def test_server_start():
    """测试服务器启动"""
    print("🔍 测试Django服务器启动...")
    
    # 启动服务器进程
    try:
        print("启动Django开发服务器...")
        process = subprocess.Popen(
            ["python", "manage.py", "runserver", "127.0.0.1:5001", "--noreload"],
            cwd=current_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待服务器启动
        print("等待服务器启动...")
        time.sleep(5)
        
        # 检查服务器是否运行
        import socket

# 配置UTF-8输出
import sys
import io
if hasattr(sys.stdout, 'buffer'):
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
if hasattr(sys.stderr, 'buffer'):
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(3)
                result = sock.connect_ex(('127.0.0.1', 5001))
                if result == 0:
                    print("✅ Django服务器启动成功")
                    server_running = True
                else:
                    print("❌ 无法连接到Django服务器")
                    server_running = False
        except Exception as e:
            print(f"❌ 检查服务器连接失败: {e}")
            server_running = False
        
        # 停止服务器
        try:
            process.terminate()
            process.wait(timeout=5)
            print("✅ Django服务器已停止")
        except subprocess.TimeoutExpired:
            process.kill()
            print("⚠️ 强制停止Django服务器")
        except Exception as e:
            print(f"⚠️ 停止服务器时出错: {e}")
        
        return server_running
        
    except Exception as e:
        print(f"❌ 启动服务器测试失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    print("🔍 测试API端点...")
    
    # 这里可以添加API测试逻辑
    # 由于需要服务器运行，暂时跳过
    print("⚠️ API端点测试需要服务器运行，暂时跳过")
    return True

def test_data_migration():
    """测试数据迁移"""
    print("🔍 测试数据迁移...")
    
    # 检查是否有Flask数据库文件
    possible_db_paths = [
        'data/dashboard.db',
        'dashboard.db',
        '../data/dashboard.db',
        '../dashboard.db'
    ]
    
    flask_db_found = False
    for db_path in possible_db_paths:
        full_path = current_dir / db_path
        if full_path.exists():
            print(f"找到Flask数据库: {full_path}")
            flask_db_found = True
            break
    
    if flask_db_found:
        return run_command("python manage.py migrate_flask_data --dry-run", "数据迁移测试")
    else:
        print("⚠️ 未找到Flask数据库文件，跳过数据迁移测试")
        return True

def main():
    """主测试函数"""
    print("🚀 开始Django迁移测试...")
    print(f"📁 当前目录: {current_dir}")
    
    tests = [
        ("Django环境", test_django_environment),
        ("生成迁移文件", test_makemigrations),
        ("执行数据库迁移", test_migrate),
        ("Django系统检查", test_check),
        ("收集静态文件", test_collectstatic),
        ("服务器启动测试", test_server_start),
        ("数据迁移测试", test_data_migration),
        ("API端点测试", test_api_endpoints),
    ]
    
    passed = 0
    total = len(tests)
    failed_tests = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                failed_tests.append(test_name)
                print(f"❌ {test_name} 失败")
        except Exception as e:
            failed_tests.append(test_name)
            print(f"❌ {test_name} 异常: {e}")
    
    # 输出测试结果
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    print(f"总测试数: {total}")
    print(f"通过数量: {passed}")
    print(f"失败数量: {len(failed_tests)}")
    print(f"通过率: {(passed/total)*100:.1f}%")
    
    if failed_tests:
        print(f"\n❌ 失败的测试:")
        for test in failed_tests:
            print(f"  - {test}")
    
    if passed == total:
        print("\n🎉 所有测试通过！Django迁移成功！")
        print("\n📋 下一步操作:")
        print("1. 启动RunSim GUI")
        print("2. 测试仪表板插件")
        print("3. 验证所有功能正常")
        return True
    else:
        print(f"\n⚠️ {len(failed_tests)} 个测试失败，请检查相关配置")
        return False

if __name__ == '__main__':
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
