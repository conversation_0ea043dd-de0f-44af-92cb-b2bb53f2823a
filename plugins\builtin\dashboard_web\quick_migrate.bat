@echo off
echo ========================================
echo Django 快速迁移脚本
echo ========================================
echo.

cd /d "%~dp0"
echo 当前目录: %CD%
echo.

echo 步骤 1: 检查Django
python -c "import django; print('Django版本:', django.VERSION)"
if errorlevel 1 (
    echo ❌ Django未安装或导入失败
    pause
    exit /b 1
)

echo.
echo 步骤 2: 生成迁移文件
python manage.py makemigrations dashboard_app
if errorlevel 1 (
    echo ❌ 生成迁移文件失败
    pause
    exit /b 1
)

echo.
echo 步骤 3: 执行迁移
python manage.py migrate
if errorlevel 1 (
    echo ❌ 执行迁移失败
    pause
    exit /b 1
)

echo.
echo 步骤 4: 系统检查
python manage.py check

echo.
echo ✅ Django迁移完成！
echo.
echo 测试服务器启动:
echo python manage.py runserver 127.0.0.1:5001
echo.
pause
