"""
Django服务器启动脚本

用于在RunSim GUI插件中启动Django应用
"""

import os
import sys
import logging
import threading
import time
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DjangoServer:
    """Django服务器管理类"""
    
    def __init__(self, port=5001):
        self.port = port
        self.server_thread = None
        self.running = False
        self.django_app = None
        
    def start(self):
        """启动Django服务器"""
        if self.running:
            logger.warning("Django服务器已在运行")
            return True
            
        try:
            # 设置Django环境
            self._setup_django_environment()
            
            # 启动服务器线程
            self.server_thread = threading.Thread(
                target=self._run_server,
                daemon=True,
                name="DjangoDashboardServer"
            )
            self.server_thread.start()
            
            # 等待服务器启动
            if self._wait_for_server_start():
                self.running = True
                logger.info(f"Django服务器启动成功，端口: {self.port}")
                return True
            else:
                logger.error("Django服务器启动超时")
                return False
                
        except Exception as e:
            logger.error(f"启动Django服务器失败: {e}")
            return False
    
    def stop(self):
        """停止Django服务器"""
        try:
            self.running = False
            if self.server_thread and self.server_thread.is_alive():
                # Django服务器会随着线程结束而停止
                logger.info("Django服务器停止")
        except Exception as e:
            logger.error(f"停止Django服务器失败: {e}")
    
    def _setup_django_environment(self):
        """设置Django环境"""
        try:
            # 获取当前目录
            current_dir = Path(__file__).parent.absolute()
            
            # 添加到Python路径
            if str(current_dir) not in sys.path:
                sys.path.insert(0, str(current_dir))
            
            # 设置Django设置模块
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dashboard_project.settings')
            
            # 保存原始工作目录
            original_cwd = os.getcwd()
            if 'RUNSIM_ORIGINAL_CWD' not in os.environ:
                os.environ['RUNSIM_ORIGINAL_CWD'] = original_cwd
            
            # 切换到Django项目目录
            os.chdir(current_dir)
            
            logger.info(f"Django环境设置完成，工作目录: {current_dir}")
            
        except Exception as e:
            logger.error(f"设置Django环境失败: {e}")
            raise
    
    def _run_server(self):
        """运行Django服务器"""
        try:
            import django
            from django.core.management import execute_from_command_line
            from django.core.wsgi import get_wsgi_application
            
            # 初始化Django
            django.setup()
            
            # 执行数据库迁移（如果需要）
            try:
                execute_from_command_line(['manage.py', 'migrate', '--run-syncdb'])
                logger.info("数据库迁移完成")
            except Exception as e:
                logger.warning(f"数据库迁移失败: {e}")
            
            # 启动开发服务器
            logger.info(f"启动Django开发服务器，端口: {self.port}")
            execute_from_command_line([
                'manage.py', 
                'runserver', 
                f'127.0.0.1:{self.port}',
                '--noreload',  # 禁用自动重载
                '--nothreading'  # 使用单线程模式
            ])
            
        except ImportError as e:
            logger.error(f"Django导入失败: {e}")
            logger.error("请确保Django已正确安装")
        except Exception as e:
            logger.error(f"运行Django服务器失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
    
    def _wait_for_server_start(self, timeout=15):
        """等待服务器启动"""
        import socket
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                    sock.settimeout(1)
                    result = sock.connect_ex(('127.0.0.1', self.port))
                    if result == 0:
                        return True
            except:
                pass
            time.sleep(0.5)
        
        return False
    
    def is_running(self):
        """检查服务器是否运行"""
        import socket
        
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(('127.0.0.1', self.port))
                return result == 0
        except:
            return False
    
    def get_url(self):
        """获取服务器URL"""
        return f"http://127.0.0.1:{self.port}"


def create_django_server(port=5001):
    """创建Django服务器实例"""
    return DjangoServer(port)


def test_django_server():
    """测试Django服务器"""
    server = create_django_server(5001)
    
    print("启动Django服务器...")
    if server.start():
        print(f"服务器启动成功: {server.get_url()}")
        
        try:
            # 保持服务器运行
            while server.is_running():
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n停止服务器...")
            server.stop()
    else:
        print("服务器启动失败")


if __name__ == '__main__':
    test_django_server()
