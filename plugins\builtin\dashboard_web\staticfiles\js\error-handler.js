/**
 * 错误处理和调试工具
 * 用于过滤和处理页面中的各种错误
 */

(function() {
    'use strict';
    
    // 错误过滤配置
    const ERROR_FILTERS = {
        // 需要过滤的URL模式
        urlPatterns: [
            'chatgpt.io',
            'api.chatgpt.io',
            'openai.com',
            'chrome-extension://',
            'moz-extension://'
        ],
        
        // 需要过滤的错误消息模式
        messagePatterns: [
            'chatgpt',
            'openai',
            'extension',
            'chrome',
            'firefox'
        ]
    };
    
    // 错误统计
    const errorStats = {
        filtered: 0,
        total: 0,
        types: {}
    };
    
    /**
     * 检查是否应该过滤错误
     */
    function shouldFilterError(message, url) {
        if (!message && !url) return false;
        
        const messageStr = String(message).toLowerCase();
        const urlStr = String(url).toLowerCase();
        
        // 检查URL模式
        for (const pattern of ERROR_FILTERS.urlPatterns) {
            if (urlStr.includes(pattern.toLowerCase())) {
                return true;
            }
        }
        
        // 检查消息模式
        for (const pattern of ERROR_FILTERS.messagePatterns) {
            if (messageStr.includes(pattern.toLowerCase())) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 记录错误统计
     */
    function recordError(type, filtered = false) {
        errorStats.total++;
        if (filtered) {
            errorStats.filtered++;
        }
        
        if (!errorStats.types[type]) {
            errorStats.types[type] = { total: 0, filtered: 0 };
        }
        errorStats.types[type].total++;
        if (filtered) {
            errorStats.types[type].filtered++;
        }
    }
    
    /**
     * 重写console.error
     */
    const originalConsoleError = console.error;
    console.error = function(...args) {
        const message = args.join(' ');
        const filtered = shouldFilterError(message, '');
        
        recordError('console', filtered);
        
        if (!filtered) {
            originalConsoleError.apply(console, args);
        } else {
            // 可选：在开发模式下显示被过滤的错误
            if (window.DEBUG_MODE) {
                console.warn('[FILTERED ERROR]', ...args);
            }
        }
    };
    
    /**
     * 重写console.warn
     */
    const originalConsoleWarn = console.warn;
    console.warn = function(...args) {
        const message = args.join(' ');
        const filtered = shouldFilterError(message, '');
        
        recordError('warning', filtered);
        
        if (!filtered) {
            originalConsoleWarn.apply(console, args);
        }
    };
    
    /**
     * 全局错误处理
     */
    window.addEventListener('error', function(event) {
        const filtered = shouldFilterError(event.message, event.filename);
        
        recordError('global', filtered);
        
        if (filtered) {
            event.preventDefault();
            return false;
        }
    });
    
    /**
     * Promise错误处理
     */
    window.addEventListener('unhandledrejection', function(event) {
        const message = event.reason ? String(event.reason) : '';
        const filtered = shouldFilterError(message, '');
        
        recordError('promise', filtered);
        
        if (filtered) {
            event.preventDefault();
            return false;
        }
    });
    
    /**
     * 网络请求拦截
     */
    if (window.fetch) {
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            const url = args[0];
            
            if (typeof url === 'string' && shouldFilterError('', url)) {
                recordError('fetch', true);
                console.warn('[BLOCKED REQUEST]', url);
                return Promise.reject(new Error('Request blocked by error handler'));
            }
            
            recordError('fetch', false);
            return originalFetch.apply(this, args);
        };
    }
    
    /**
     * XMLHttpRequest拦截
     */
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        if (shouldFilterError('', url)) {
            recordError('xhr', true);
            console.warn('[BLOCKED XHR]', url);
            throw new Error('XHR request blocked by error handler');
        }
        
        recordError('xhr', false);
        return originalXHROpen.call(this, method, url, ...args);
    };
    
    /**
     * 获取错误统计
     */
    window.getErrorStats = function() {
        return {
            ...errorStats,
            filterRate: errorStats.total > 0 ? (errorStats.filtered / errorStats.total * 100).toFixed(2) + '%' : '0%'
        };
    };
    
    /**
     * 清除错误统计
     */
    window.clearErrorStats = function() {
        errorStats.filtered = 0;
        errorStats.total = 0;
        errorStats.types = {};
        console.log('错误统计已清除');
    };
    
    /**
     * 显示错误统计
     */
    window.showErrorStats = function() {
        console.group('错误统计');
        console.log('总错误数:', errorStats.total);
        console.log('已过滤:', errorStats.filtered);
        console.log('过滤率:', errorStats.total > 0 ? (errorStats.filtered / errorStats.total * 100).toFixed(2) + '%' : '0%');
        console.log('错误类型:', errorStats.types);
        console.groupEnd();
    };
    
    /**
     * 启用调试模式
     */
    window.enableDebugMode = function() {
        window.DEBUG_MODE = true;
        console.log('调试模式已启用，将显示被过滤的错误');
    };
    
    /**
     * 禁用调试模式
     */
    window.disableDebugMode = function() {
        window.DEBUG_MODE = false;
        console.log('调试模式已禁用');
    };
    
    // 页面加载完成后显示初始化信息
    document.addEventListener('DOMContentLoaded', function() {
        console.log('错误处理器已初始化');
        console.log('可用命令: getErrorStats(), showErrorStats(), clearErrorStats(), enableDebugMode(), disableDebugMode()');
    });
    
    // 定期显示错误统计（仅在开发模式下）
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        setInterval(function() {
            if (errorStats.filtered > 0) {
                console.log(`[错误过滤器] 已过滤 ${errorStats.filtered} 个错误`);
            }
        }, 30000); // 每30秒检查一次
    }
    
})();
