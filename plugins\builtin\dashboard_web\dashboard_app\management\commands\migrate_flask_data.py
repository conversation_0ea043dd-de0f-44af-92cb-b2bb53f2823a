"""
Django管理命令：迁移Flask数据
"""

from django.core.management.base import BaseCommand
from django.db import transaction
import sqlite3
import os
from pathlib import Path
from dashboard_app.models import Project, TestCase, Bug, CaseStatusHistory, SystemConfig


class Command(BaseCommand):
    help = '从Flask数据库迁移数据到Django数据库'

    def add_arguments(self, parser):
        parser.add_argument(
            '--flask-db',
            type=str,
            help='Flask数据库文件路径',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='试运行，不实际写入数据',
        )

    def handle(self, *args, **options):
        flask_db_path = options.get('flask_db')
        dry_run = options.get('dry_run', False)
        
        if not flask_db_path:
            flask_db_path = self.find_flask_database()
        
        if not flask_db_path:
            self.stdout.write(
                self.style.ERROR('未找到Flask数据库文件')
            )
            return
        
        if not os.path.exists(flask_db_path):
            self.stdout.write(
                self.style.ERROR(f'数据库文件不存在: {flask_db_path}')
            )
            return
        
        self.stdout.write(f'开始迁移数据从: {flask_db_path}')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('试运行模式 - 不会实际写入数据'))
        
        try:
            with transaction.atomic():
                success = self.migrate_data(flask_db_path, dry_run)
                
                if dry_run:
                    # 试运行时回滚事务
                    transaction.set_rollback(True)
                    self.stdout.write(self.style.SUCCESS('试运行完成'))
                elif success:
                    self.stdout.write(self.style.SUCCESS('数据迁移完成'))
                else:
                    self.stdout.write(self.style.ERROR('数据迁移失败'))
                    
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'迁移过程中发生错误: {e}'))
    
    def find_flask_database(self):
        """查找Flask数据库文件"""
        current_dir = Path(__file__).parent.parent.parent.parent
        possible_paths = [
            'data/dashboard.db',
            'dashboard.db',
            'runsim_dashboard.db',
            '../data/dashboard.db',
            '../dashboard.db',
            '../runsim_dashboard.db',
        ]
        
        for path in possible_paths:
            full_path = current_dir / path
            if full_path.exists():
                return str(full_path)
        
        return None
    
    def migrate_data(self, flask_db_path, dry_run=False):
        """执行数据迁移"""
        conn = sqlite3.connect(flask_db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            # 迁移项目
            self.migrate_projects(cursor, dry_run)
            
            # 迁移测试用例
            self.migrate_test_cases(cursor, dry_run)
            
            # 迁移BUG
            self.migrate_bugs(cursor, dry_run)
            
            # 迁移系统配置
            self.migrate_system_config(cursor, dry_run)
            
            # 迁移状态历史
            self.migrate_case_status_history(cursor, dry_run)
            
            return True
            
        finally:
            conn.close()
    
    def migrate_projects(self, cursor, dry_run):
        """迁移项目数据"""
        self.stdout.write('迁移项目数据...')
        
        try:
            cursor.execute("SELECT * FROM projects")
            projects = cursor.fetchall()
            
            migrated_count = 0
            for row in projects:
                if not dry_run:
                    project, created = Project.objects.get_or_create(
                        id=row['id'],
                        defaults={
                            'name': row['name'] or '默认项目',
                            'subsystem': row['subsystem'] or 'default',
                            'description': row['description'] or '',
                        }
                    )
                    if created:
                        migrated_count += 1
                else:
                    migrated_count += 1
                    
            self.stdout.write(f'  项目: {migrated_count} 个')
            
        except sqlite3.OperationalError as e:
            if "no such table: projects" in str(e):
                self.stdout.write('  projects表不存在，创建默认项目')
                if not dry_run:
                    Project.objects.get_or_create(
                        name='默认项目',
                        defaults={
                            'subsystem': 'default',
                            'description': '系统默认项目'
                        }
                    )
    
    def migrate_test_cases(self, cursor, dry_run):
        """迁移测试用例数据"""
        self.stdout.write('迁移测试用例数据...')
        
        try:
            cursor.execute("SELECT * FROM test_cases")
            cases = cursor.fetchall()
            
            migrated_count = 0
            for row in cases:
                if not dry_run:
                    # 获取项目
                    project_id = row.get('project_id', 1)
                    try:
                        project = Project.objects.get(id=project_id)
                    except Project.DoesNotExist:
                        project = Project.objects.first()
                        if not project:
                            project = Project.objects.create(
                                name='默认项目',
                                subsystem='default',
                                description='系统默认项目'
                            )
                    
                    test_case, created = TestCase.objects.get_or_create(
                        id=row['id'],
                        defaults={
                            'project': project,
                            'case_name': row.get('case_name', ''),
                            'category': row.get('category', ''),
                            'number': row.get('number', ''),
                            'test_areas': row.get('test_areas', ''),
                            'test_scope': row.get('test_scope', ''),
                            'function_point': row.get('function_point', ''),
                            'check_point': row.get('check_point', ''),
                            'cover': row.get('cover', ''),
                            'owner': row.get('owner', ''),
                            'subsys_phase': row.get('subsys_phase', ''),
                            'subsys_status': row.get('subsys_status', 'Pending'),
                            'top_phase': row.get('top_phase', ''),
                            'top_status': row.get('top_status', 'Pending'),
                            'post_subsys_phase': row.get('post_subsys_phase', ''),
                            'post_subsys_status': row.get('post_subsys_status', 'Pending'),
                            'post_top_phase': row.get('post_top_phase', ''),
                            'post_top_status': row.get('post_top_status', 'Pending'),
                            'remarks': row.get('remarks', ''),
                            'actual_time': row.get('actual_time'),
                        }
                    )
                    if created:
                        migrated_count += 1
                else:
                    migrated_count += 1
                    
            self.stdout.write(f'  测试用例: {migrated_count} 个')
            
        except sqlite3.OperationalError as e:
            if "no such table: test_cases" in str(e):
                self.stdout.write('  test_cases表不存在，跳过')
    
    def migrate_bugs(self, cursor, dry_run):
        """迁移BUG数据"""
        self.stdout.write('迁移BUG数据...')
        
        try:
            cursor.execute("SELECT * FROM bugs")
            bugs = cursor.fetchall()
            
            migrated_count = 0
            for row in bugs:
                if not dry_run:
                    project_id = row.get('project_id', 1)
                    try:
                        project = Project.objects.get(id=project_id)
                    except Project.DoesNotExist:
                        project = Project.objects.first()
                    
                    bug, created = Bug.objects.get_or_create(
                        id=row['id'],
                        defaults={
                            'project': project,
                            'bug_id': row.get('bug_id', ''),
                            'bug_type': row.get('bug_type', ''),
                            'description': row.get('description', ''),
                            'severity': row.get('severity', 'Medium'),
                            'status': row.get('status', 'Open'),
                            'submitter': row.get('submitter', ''),
                        }
                    )
                    if created:
                        migrated_count += 1
                else:
                    migrated_count += 1
                    
            self.stdout.write(f'  BUG: {migrated_count} 个')
            
        except sqlite3.OperationalError as e:
            if "no such table: bugs" in str(e):
                self.stdout.write('  bugs表不存在，跳过')
    
    def migrate_system_config(self, cursor, dry_run):
        """迁移系统配置"""
        self.stdout.write('迁移系统配置...')
        
        try:
            cursor.execute("SELECT * FROM system_config")
            configs = cursor.fetchall()
            
            migrated_count = 0
            for row in configs:
                if not dry_run:
                    config, created = SystemConfig.objects.get_or_create(
                        config_key=row['config_key'],
                        defaults={
                            'config_value': row.get('config_value', ''),
                            'description': row.get('description', ''),
                        }
                    )
                    if created:
                        migrated_count += 1
                else:
                    migrated_count += 1
                    
            self.stdout.write(f'  系统配置: {migrated_count} 个')
            
        except sqlite3.OperationalError as e:
            if "no such table: system_config" in str(e):
                self.stdout.write('  system_config表不存在，跳过')
    
    def migrate_case_status_history(self, cursor, dry_run):
        """迁移状态历史"""
        self.stdout.write('迁移状态历史...')
        
        try:
            cursor.execute("SELECT * FROM case_status_history")
            histories = cursor.fetchall()
            
            migrated_count = 0
            for row in histories:
                if not dry_run:
                    try:
                        case = TestCase.objects.get(id=row['case_id'])
                        history, created = CaseStatusHistory.objects.get_or_create(
                            id=row['id'],
                            defaults={
                                'case': case,
                                'old_status': row.get('old_status', ''),
                                'new_status': row.get('new_status', ''),
                                'stage_type': row.get('stage_type', ''),
                                'changed_by': row.get('changed_by', ''),
                            }
                        )
                        if created:
                            migrated_count += 1
                    except TestCase.DoesNotExist:
                        pass
                else:
                    migrated_count += 1
                    
            self.stdout.write(f'  状态历史: {migrated_count} 个')
            
        except sqlite3.OperationalError as e:
            if "no such table: case_status_history" in str(e):
                self.stdout.write('  case_status_history表不存在，跳过')
