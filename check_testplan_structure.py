#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查TestPlan表格结构
"""

import openpyxl

# 配置UTF-8输出
import sys
import io
if hasattr(sys.stdout, 'buffer'):
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
if hasattr(sys.stderr, 'buffer'):
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')


def check_testplan_structure():
    """检查TestPlan表格结构"""
    try:
        wb = openpyxl.load_workbook("TestPlan_Template.xlsx")
        ws = wb["TP"]
        
        print("📋 TestPlan表格结构检查")
        print("=" * 50)
        
        # 检查表头
        print("🔍 表头信息:")
        headers_row3 = []
        headers_row4 = []
        
        for col in range(1, 22):  # A到U列
            val3 = ws.cell(row=3, column=col).value
            val4 = ws.cell(row=4, column=col).value
            headers_row3.append(val3 if val3 else "")
            headers_row4.append(val4 if val4 else "")
        
        print("第3行表头:")
        for i, header in enumerate(headers_row3):
            if header:
                col_letter = chr(65 + i)  # A, B, C...
                print(f"  {col_letter}: {header}")
        
        print("\n第4行表头:")
        for i, header in enumerate(headers_row4):
            if header:
                col_letter = chr(65 + i)  # A, B, C...
                print(f"  {col_letter}: {header}")
        
        # 检查数据行
        print(f"\n📊 数据信息:")
        print(f"总行数: {ws.max_row}")
        print(f"总列数: {ws.max_column}")
        
        # 检查示例数据
        print(f"\n📝 示例数据 (前5行):")
        for row in range(5, min(10, ws.max_row + 1)):
            category = ws.cell(row=row, column=1).value  # A列
            items = ws.cell(row=row, column=2).value     # B列
            case_name = ws.cell(row=row, column=8).value # H列 (Case Name)
            if category or items or case_name:
                print(f"  第{row}行: {category} | {items} | {case_name}")
        
        wb.close()
        print("\n✅ 表格结构检查完成!")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_testplan_structure()
