"""
Django views for RunSim Dashboard

这些视图基于原有的Flask路由重写，保持API兼容性。
"""

import json
import logging
from datetime import datetime, timedelta
from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse, Http404
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum
from django.db import transaction
from django.utils import timezone
from django.conf import settings

from .models import Project, TestCase, Bug, CaseStatusHistory, SystemConfig, PhaseStatistics

logger = logging.getLogger(__name__)


# ============================================================================
# 页面视图
# ============================================================================

def dashboard_view(request):
    """仪表板主页"""
    try:
        return render(request, 'dashboard.html', {
            'title': 'RunSim 项目仪表板',
            'current_time': timezone.now(),
        })
    except Exception as e:
        logger.error(f"渲染仪表板页面失败: {e}")
        return render(request, 'error.html', {
            'error_code': 500,
            'error_message': '页面加载失败'
        }, status=500)


def testplan_view(request):
    """用例管理页面"""
    try:
        return render(request, 'testplan.html', {
            'title': '用例管理 - RunSim 项目仪表板',
            'current_time': timezone.now(),
        })
    except Exception as e:
        logger.error(f"渲染用例管理页面失败: {e}")
        # 尝试简化版本
        try:
            return render(request, 'testplan_simple.html', {
                'title': '用例管理 - RunSim 项目仪表板',
                'current_time': timezone.now(),
            })
        except Exception as e2:
            logger.error(f"渲染简化版页面也失败: {e2}")
            return render(request, 'error.html', {
                'error_code': 500,
                'error_message': '页面加载失败'
            }, status=500)


def bug_view(request):
    """BUG管理页面"""
    try:
        return render(request, 'bug.html', {
            'title': 'BUG管理 - RunSim 项目仪表板',
            'current_time': timezone.now(),
        })
    except Exception as e:
        logger.error(f"渲染BUG管理页面失败: {e}")
        # 尝试简化版本
        try:
            return render(request, 'bug_simple.html', {
                'title': 'BUG管理 - RunSim 项目仪表板',
                'current_time': timezone.now(),
            })
        except Exception as e2:
            logger.error(f"渲染简化版BUG页面也失败: {e2}")
            return render(request, 'error.html', {
                'error_code': 500,
                'error_message': '页面加载失败'
            }, status=500)


def health_check(request):
    """健康检查页面"""
    try:
        # 检查数据库连接
        Project.objects.first()
        
        return JsonResponse({
            'status': 'healthy',
            'timestamp': timezone.now().isoformat(),
            'database': 'connected',
            'version': '2.0.0-django'
        })
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JsonResponse({
            'status': 'unhealthy',
            'timestamp': timezone.now().isoformat(),
            'error': str(e)
        }, status=500)


# ============================================================================
# API视图
# ============================================================================

def api_health_check(request):
    """API健康检查"""
    try:
        # 检查数据库连接
        Project.objects.first()
        
        return JsonResponse({
            'status': 'healthy',
            'timestamp': timezone.now().isoformat(),
            'database': 'connected',
            'version': '2.0.0-django'
        })
    except Exception as e:
        logger.error(f"API健康检查失败: {e}")
        return JsonResponse({
            'status': 'unhealthy',
            'timestamp': timezone.now().isoformat(),
            'error': str(e)
        }, status=500)


def dashboard_statistics(request):
    """获取仪表板统计数据"""
    try:
        # 获取项目统计
        total_projects = Project.objects.count()
        
        # 获取用例统计
        total_cases = TestCase.objects.count()
        
        # 按状态统计用例
        subsys_stats = TestCase.objects.values('subsys_status').annotate(count=Count('id'))
        top_stats = TestCase.objects.values('top_status').annotate(count=Count('id'))
        
        # 获取BUG统计
        total_bugs = Bug.objects.count()
        open_bugs = Bug.objects.filter(status='Open').count()
        
        # 构建响应数据
        stats_data = {
            'projects': {
                'total': total_projects,
            },
            'test_cases': {
                'total': total_cases,
                'subsys': {stat['subsys_status']: stat['count'] for stat in subsys_stats},
                'top': {stat['top_status']: stat['count'] for stat in top_stats},
            },
            'bugs': {
                'total': total_bugs,
                'open': open_bugs,
                'closed': total_bugs - open_bugs,
            }
        }
        
        return JsonResponse({
            'success': True,
            'data': stats_data,
            'timestamp': timezone.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取仪表板统计数据失败: {e}")
        return JsonResponse({
            'success': False,
            'error': '获取统计数据失败',
            'message': str(e)
        }, status=500)


def case_pass_rate(request):
    """获取用例通过率统计"""
    try:
        # 获取查询参数
        view_type = request.GET.get('view', 'daily')  # daily 或 weekly
        days = int(request.GET.get('days', 30))
        
        # 计算日期范围
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # 查询用例数据
        cases = TestCase.objects.filter(
            updated_at__date__gte=start_date,
            updated_at__date__lte=end_date
        )
        
        # 按日期分组统计
        pass_rate_data = []
        current_date = start_date
        
        while current_date <= end_date:
            day_cases = cases.filter(updated_at__date=current_date)
            total = day_cases.count()
            passed = day_cases.filter(
                Q(subsys_status='PASS') | Q(top_status='PASS')
            ).count()
            
            pass_rate = (passed / total * 100) if total > 0 else 0
            
            pass_rate_data.append({
                'date': current_date.isoformat(),
                'total_cases': total,
                'passed_cases': passed,
                'pass_rate': round(pass_rate, 2)
            })
            
            current_date += timedelta(days=1)
        
        return JsonResponse({
            'success': True,
            'data': {
                'view_type': view_type,
                'date_range': {
                    'start': start_date.isoformat(),
                    'end': end_date.isoformat()
                },
                'pass_rate_data': pass_rate_data
            },
            'timestamp': timezone.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取用例通过率统计失败: {e}")
        return JsonResponse({
            'success': False,
            'error': '获取通过率统计失败',
            'message': str(e)
        }, status=500)


def bug_statistics(request):
    """获取BUG统计数据"""
    try:
        # 按状态统计
        status_stats = Bug.objects.values('status').annotate(count=Count('id'))
        
        # 按严重程度统计
        severity_stats = Bug.objects.values('severity').annotate(count=Count('id'))
        
        # 按类型统计
        type_stats = Bug.objects.values('bug_type').annotate(count=Count('id'))
        
        # 趋势数据（最近30天）
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
        
        trend_data = []
        current_date = start_date
        
        while current_date <= end_date:
            day_bugs = Bug.objects.filter(created_at__date=current_date)
            trend_data.append({
                'date': current_date.isoformat(),
                'count': day_bugs.count()
            })
            current_date += timedelta(days=1)
        
        return JsonResponse({
            'success': True,
            'data': {
                'status_distribution': {stat['status']: stat['count'] for stat in status_stats},
                'severity_distribution': {stat['severity']: stat['count'] for stat in severity_stats},
                'type_distribution': {stat['bug_type']: stat['count'] for stat in type_stats if stat['bug_type']},
                'trend_data': trend_data
            },
            'timestamp': timezone.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取BUG统计数据失败: {e}")
        return JsonResponse({
            'success': False,
            'error': '获取BUG统计失败',
            'message': str(e)
        }, status=500)


def testplan_cases(request):
    """获取测试用例列表"""
    try:
        # 获取查询参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 50))
        search = request.GET.get('search', '')
        status_filter = request.GET.get('status', '')
        phase_filter = request.GET.get('phase', '')
        
        # 构建查询
        queryset = TestCase.objects.all()
        
        if search:
            queryset = queryset.filter(
                Q(case_name__icontains=search) |
                Q(test_areas__icontains=search) |
                Q(owner__icontains=search)
            )
        
        if status_filter:
            queryset = queryset.filter(
                Q(subsys_status=status_filter) |
                Q(top_status=status_filter)
            )
        
        if phase_filter:
            queryset = queryset.filter(
                Q(subsys_phase=phase_filter) |
                Q(top_phase=phase_filter)
            )
        
        # 分页
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)
        
        # 序列化数据
        cases_data = []
        for case in page_obj:
            cases_data.append({
                'id': case.id,
                'case_name': case.case_name,
                'test_areas': case.test_areas,
                'test_scope': case.test_scope,
                'owner': case.owner,
                'subsys_phase': case.subsys_phase,
                'subsys_status': case.subsys_status,
                'top_phase': case.top_phase,
                'top_status': case.top_status,
                'post_subsys_phase': case.post_subsys_phase,
                'post_subsys_status': case.post_subsys_status,
                'post_top_phase': case.post_top_phase,
                'post_top_status': case.post_top_status,
                'created_at': case.created_at.isoformat(),
                'updated_at': case.updated_at.isoformat(),
            })
        
        return JsonResponse({
            'success': True,
            'data': {
                'cases': cases_data,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': paginator.count,
                    'total_pages': paginator.num_pages,
                    'has_next': page_obj.has_next(),
                    'has_prev': page_obj.has_previous(),
                }
            },
            'timestamp': timezone.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取测试用例列表失败: {e}")
        return JsonResponse({
            'success': False,
            'error': '获取用例列表失败',
            'message': str(e)
        }, status=500)


def testplan_statistics(request):
    """获取测试计划统计数据"""
    try:
        # 总体统计
        total_cases = TestCase.objects.count()
        
        # 按状态统计
        status_counts = {
            'subsys': {
                'pass': TestCase.objects.filter(subsys_status='PASS').count(),
                'pending': TestCase.objects.filter(subsys_status='Pending').count(),
                'ongoing': TestCase.objects.filter(subsys_status='On-Going').count(),
                'na': TestCase.objects.filter(subsys_status='N/A').count(),
            },
            'top': {
                'pass': TestCase.objects.filter(top_status='PASS').count(),
                'pending': TestCase.objects.filter(top_status='Pending').count(),
                'ongoing': TestCase.objects.filter(top_status='On-Going').count(),
                'na': TestCase.objects.filter(top_status='N/A').count(),
            },
            'post_subsys': {
                'pass': TestCase.objects.filter(post_subsys_status='PASS').count(),
                'pending': TestCase.objects.filter(post_subsys_status='Pending').count(),
                'ongoing': TestCase.objects.filter(post_subsys_status='On-Going').count(),
                'na': TestCase.objects.filter(post_subsys_status='N/A').count(),
            },
            'post_top': {
                'pass': TestCase.objects.filter(post_top_status='PASS').count(),
                'pending': TestCase.objects.filter(post_top_status='Pending').count(),
                'ongoing': TestCase.objects.filter(post_top_status='On-Going').count(),
                'na': TestCase.objects.filter(post_top_status='N/A').count(),
            }
        }
        
        return JsonResponse({
            'success': True,
            'data': {
                'total_cases': total_cases,
                'status_counts': status_counts,
            },
            'timestamp': timezone.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取测试计划统计失败: {e}")
        return JsonResponse({
            'success': False,
            'error': '获取统计信息失败',
            'message': str(e)
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def testplan_import(request):
    """导入测试计划Excel文件"""
    try:
        if 'file' not in request.FILES:
            return JsonResponse({
                'success': False,
                'error': '没有上传文件'
            }, status=400)

        uploaded_file = request.FILES['file']

        # 这里需要实现Excel解析逻辑
        # 暂时返回成功响应
        return JsonResponse({
            'success': True,
            'message': f'文件 {uploaded_file.name} 上传成功',
            'data': {
                'filename': uploaded_file.name,
                'size': uploaded_file.size,
            },
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        logger.error(f"导入测试计划失败: {e}")
        return JsonResponse({
            'success': False,
            'error': '导入失败',
            'message': str(e)
        }, status=500)


def testplan_export(request):
    """导出测试计划"""
    try:
        # 这里需要实现Excel导出逻辑
        # 暂时返回成功响应
        return JsonResponse({
            'success': True,
            'message': '导出功能开发中',
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        logger.error(f"导出测试计划失败: {e}")
        return JsonResponse({
            'success': False,
            'error': '导出失败',
            'message': str(e)
        }, status=500)


def testplan_template(request):
    """下载测试计划模板"""
    try:
        # 这里需要实现模板下载逻辑
        return JsonResponse({
            'success': False,
            'error': '模板下载功能暂不可用',
            'message': '请联系管理员'
        }, status=503)

    except Exception as e:
        logger.error(f"下载模板失败: {e}")
        return JsonResponse({
            'success': False,
            'error': '下载失败',
            'message': str(e)
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def update_from_runsim(request):
    """从RunSim GUI更新用例状态"""
    try:
        data = json.loads(request.body)

        case_name = data.get('case_name')
        status = data.get('status', 'PASS')
        stage_type = data.get('stage_type', 'subsys')

        if not case_name:
            return JsonResponse({
                'success': False,
                'error': '缺少用例名称'
            }, status=400)

        # 查找用例
        try:
            case = TestCase.objects.get(case_name=case_name)
        except TestCase.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': '用例不存在'
            }, status=404)

        # 更新状态
        old_status = None
        if stage_type == 'subsys':
            old_status = case.subsys_status
            case.subsys_status = status
        elif stage_type == 'top':
            old_status = case.top_status
            case.top_status = status
        elif stage_type == 'post_subsys':
            old_status = case.post_subsys_status
            case.post_subsys_status = status
        elif stage_type == 'post_top':
            old_status = case.post_top_status
            case.post_top_status = status

        case.save()

        # 记录状态变更历史
        CaseStatusHistory.objects.create(
            case=case,
            old_status=old_status,
            new_status=status,
            stage_type=stage_type,
            changed_by='runsim_gui'
        )

        return JsonResponse({
            'success': True,
            'message': f'用例状态更新成功: {case_name}',
            'timestamp': timezone.now().isoformat()
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': '无效的JSON数据'
        }, status=400)
    except Exception as e:
        logger.error(f"从RunSim GUI更新用例状态失败: {e}")
        return JsonResponse({
            'success': False,
            'error': '更新失败',
            'message': str(e)
        }, status=500)


def get_case_detail(request, case_id):
    """获取用例详情"""
    try:
        case = get_object_or_404(TestCase, id=case_id)

        case_data = {
            'id': case.id,
            'case_name': case.case_name,
            'category': case.category,
            'number': case.number,
            'test_areas': case.test_areas,
            'test_scope': case.test_scope,
            'function_point': case.function_point,
            'check_point': case.check_point,
            'cover': case.cover,
            'owner': case.owner,
            'subsys_phase': case.subsys_phase,
            'subsys_status': case.subsys_status,
            'top_phase': case.top_phase,
            'top_status': case.top_status,
            'post_subsys_phase': case.post_subsys_phase,
            'post_subsys_status': case.post_subsys_status,
            'post_top_phase': case.post_top_phase,
            'post_top_status': case.post_top_status,
            'remarks': case.remarks,
            'start_time': case.start_time.isoformat() if case.start_time else None,
            'end_time': case.end_time.isoformat() if case.end_time else None,
            'actual_time': case.actual_time,
            'created_at': case.created_at.isoformat(),
            'updated_at': case.updated_at.isoformat(),
        }

        return JsonResponse({
            'success': True,
            'data': case_data,
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取用例详情失败: {e}")
        return JsonResponse({
            'success': False,
            'error': '获取用例详情失败',
            'message': str(e)
        }, status=500)


@csrf_exempt
def bugs_api(request):
    """BUG管理API"""
    if request.method == 'GET':
        return _get_bugs_list(request)
    elif request.method == 'POST':
        return _create_bug(request)
    else:
        return JsonResponse({
            'success': False,
            'error': '不支持的HTTP方法'
        }, status=405)


def _get_bugs_list(request):
    """获取BUG列表"""
    try:
        # 获取查询参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        search = request.GET.get('search', '')
        status_filter = request.GET.get('status', '')
        severity_filter = request.GET.get('severity', '')

        # 构建查询
        queryset = Bug.objects.all()

        if search:
            queryset = queryset.filter(
                Q(bug_id__icontains=search) |
                Q(description__icontains=search) |
                Q(submitter__icontains=search)
            )

        if status_filter:
            queryset = queryset.filter(status=status_filter)

        if severity_filter:
            queryset = queryset.filter(severity=severity_filter)

        # 分页
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # 序列化数据
        bugs_data = []
        for bug in page_obj:
            bugs_data.append({
                'id': bug.id,
                'bug_id': bug.bug_id,
                'bug_type': bug.bug_type,
                'description': bug.description,
                'severity': bug.severity,
                'status': bug.status,
                'submitter': bug.submitter,
                'verifier': bug.verifier,
                'submit_date': bug.submit_date.isoformat() if bug.submit_date else None,
                'fix_date': bug.fix_date.isoformat() if bug.fix_date else None,
                'created_at': bug.created_at.isoformat(),
                'updated_at': bug.updated_at.isoformat(),
            })

        return JsonResponse({
            'success': True,
            'data': {
                'bugs': bugs_data,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': paginator.count,
                    'total_pages': paginator.num_pages,
                    'has_next': page_obj.has_next(),
                    'has_prev': page_obj.has_previous(),
                }
            },
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取BUG列表失败: {e}")
        return JsonResponse({
            'success': False,
            'error': '获取BUG列表失败',
            'message': str(e)
        }, status=500)


def _create_bug(request):
    """创建新BUG"""
    try:
        data = json.loads(request.body)

        # 获取或创建默认项目
        project, created = Project.objects.get_or_create(
            name='默认项目',
            defaults={'subsystem': 'default', 'description': '系统默认项目'}
        )

        # 创建BUG
        bug = Bug.objects.create(
            project=project,
            bug_id=data.get('bug_id', ''),
            bug_type=data.get('bug_type', ''),
            submit_sys=data.get('submit_sys', ''),
            verification_stage=data.get('verification_stage', ''),
            description=data.get('description', ''),
            discovery_platform=data.get('discovery_platform', ''),
            discovery_case=data.get('discovery_case', ''),
            severity=data.get('severity', 'Medium'),
            status=data.get('status', 'Open'),
            submitter=data.get('submitter', ''),
            verifier=data.get('verifier', ''),
            submit_date=data.get('submit_date'),
            fix_date=data.get('fix_date'),
        )

        return JsonResponse({
            'success': True,
            'message': 'BUG创建成功',
            'data': {
                'id': bug.id,
                'bug_id': bug.bug_id,
            },
            'timestamp': timezone.now().isoformat()
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': '无效的JSON数据'
        }, status=400)
    except Exception as e:
        logger.error(f"创建BUG失败: {e}")
        return JsonResponse({
            'success': False,
            'error': '创建BUG失败',
            'message': str(e)
        }, status=500)


@csrf_exempt
def bug_detail_api(request, bug_id):
    """BUG详情API"""
    if request.method == 'GET':
        return _get_bug_detail(request, bug_id)
    elif request.method == 'PUT':
        return _update_bug(request, bug_id)
    elif request.method == 'DELETE':
        return _delete_bug(request, bug_id)
    else:
        return JsonResponse({
            'success': False,
            'error': '不支持的HTTP方法'
        }, status=405)


def _get_bug_detail(request, bug_id):
    """获取BUG详情"""
    try:
        bug = get_object_or_404(Bug, id=bug_id)

        bug_data = {
            'id': bug.id,
            'bug_id': bug.bug_id,
            'bug_type': bug.bug_type,
            'submit_sys': bug.submit_sys,
            'verification_stage': bug.verification_stage,
            'description': bug.description,
            'discovery_platform': bug.discovery_platform,
            'discovery_case': bug.discovery_case,
            'severity': bug.severity,
            'status': bug.status,
            'submitter': bug.submitter,
            'verifier': bug.verifier,
            'submit_date': bug.submit_date.isoformat() if bug.submit_date else None,
            'fix_date': bug.fix_date.isoformat() if bug.fix_date else None,
            'created_at': bug.created_at.isoformat(),
            'updated_at': bug.updated_at.isoformat(),
        }

        return JsonResponse({
            'success': True,
            'data': bug_data,
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取BUG详情失败: {e}")
        return JsonResponse({
            'success': False,
            'error': '获取BUG详情失败',
            'message': str(e)
        }, status=500)


def _update_bug(request, bug_id):
    """更新BUG"""
    try:
        bug = get_object_or_404(Bug, id=bug_id)
        data = json.loads(request.body)

        # 更新字段
        for field in ['bug_id', 'bug_type', 'submit_sys', 'verification_stage',
                     'description', 'discovery_platform', 'discovery_case',
                     'severity', 'status', 'submitter', 'verifier']:
            if field in data:
                setattr(bug, field, data[field])

        if 'submit_date' in data:
            bug.submit_date = data['submit_date']
        if 'fix_date' in data:
            bug.fix_date = data['fix_date']

        bug.save()

        return JsonResponse({
            'success': True,
            'message': 'BUG更新成功',
            'timestamp': timezone.now().isoformat()
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': '无效的JSON数据'
        }, status=400)
    except Exception as e:
        logger.error(f"更新BUG失败: {e}")
        return JsonResponse({
            'success': False,
            'error': '更新BUG失败',
            'message': str(e)
        }, status=500)


def _delete_bug(request, bug_id):
    """删除BUG"""
    try:
        bug = get_object_or_404(Bug, id=bug_id)
        bug.delete()

        return JsonResponse({
            'success': True,
            'message': 'BUG删除成功',
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        logger.error(f"删除BUG失败: {e}")
        return JsonResponse({
            'success': False,
            'error': '删除BUG失败',
            'message': str(e)
        }, status=500)


def bugs_statistics(request):
    """BUG统计API"""
    try:
        # 按状态统计
        status_stats = Bug.objects.values('status').annotate(count=Count('id'))

        # 按严重程度统计
        severity_stats = Bug.objects.values('severity').annotate(count=Count('id'))

        # 按提交系统统计
        system_stats = Bug.objects.values('submit_sys').annotate(count=Count('id'))

        # 最近30天趋势
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)

        trend_data = []
        current_date = start_date

        while current_date <= end_date:
            day_bugs = Bug.objects.filter(created_at__date=current_date)
            trend_data.append({
                'date': current_date.isoformat(),
                'count': day_bugs.count()
            })
            current_date += timedelta(days=1)

        return JsonResponse({
            'success': True,
            'data': {
                'status_distribution': {stat['status']: stat['count'] for stat in status_stats},
                'severity_distribution': {stat['severity']: stat['count'] for stat in severity_stats},
                'system_distribution': {stat['submit_sys']: stat['count'] for stat in system_stats if stat['submit_sys']},
                'trend_data': trend_data,
                'total_bugs': Bug.objects.count(),
                'open_bugs': Bug.objects.filter(status='Open').count(),
            },
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取BUG统计失败: {e}")
        return JsonResponse({
            'success': False,
            'error': '获取BUG统计失败',
            'message': str(e)
        }, status=500)


def system_config(request):
    """系统配置API"""
    try:
        if request.method == 'GET':
            configs = SystemConfig.objects.all()
            config_data = {
                config.config_key: {
                    'value': config.config_value,
                    'description': config.description
                } for config in configs
            }

            return JsonResponse({
                'success': True,
                'configs': config_data,
                'timestamp': timezone.now().isoformat()
            })

        elif request.method == 'POST':
            data = json.loads(request.body)

            for key, value in data.items():
                config, created = SystemConfig.objects.get_or_create(
                    config_key=key,
                    defaults={'config_value': value}
                )
                if not created:
                    config.config_value = value
                    config.save()

            return JsonResponse({
                'success': True,
                'message': '配置更新成功',
                'timestamp': timezone.now().isoformat()
            })

        else:
            return JsonResponse({
                'success': False,
                'error': '不支持的HTTP方法'
            }, status=405)

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': '无效的JSON数据'
        }, status=400)
    except Exception as e:
        logger.error(f"系统配置操作失败: {e}")
        return JsonResponse({
            'success': False,
            'error': '系统配置操作失败',
            'message': str(e)
        }, status=500)
