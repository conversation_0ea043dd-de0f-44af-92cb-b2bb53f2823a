#!/usr/bin/env python
"""
Django环境测试脚本
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

def test_django_import():
    """测试Django导入"""
    try:
        import django
        print(f"✅ Django导入成功，版本: {django.VERSION}")
        return True
    except ImportError as e:
        print(f"❌ Django导入失败: {e}")
        return False

def test_django_setup():
    """测试Django设置"""
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dashboard_project.settings')
        import django
        django.setup()
        print("✅ Django设置成功")
        return True
    except Exception as e:
        print(f"❌ Django设置失败: {e}")
        return False

def test_models_import():
    """测试模型导入"""
    try:
        from dashboard_app.models import Project, TestCase, Bug, CaseStatusHistory, SystemConfig
        print("✅ 模型导入成功")
        return True
    except Exception as e:
        print(f"❌ 模型导入失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    try:
        from django.db import connection
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        print("✅ 数据库连接成功")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_migrations_needed():
    """检查是否需要迁移"""
    try:
        from django.core.management import execute_from_command_line
        from io import StringIO
        import sys
        
        # 捕获输出
        old_stdout = sys.stdout
        sys.stdout = mystdout = StringIO()
        
        try:
            execute_from_command_line(['manage.py', 'showmigrations', '--plan'])
            output = mystdout.getvalue()
            sys.stdout = old_stdout
            
            if '[ ]' in output:
                print("⚠️ 需要执行数据库迁移")
                return False
            else:
                print("✅ 数据库迁移状态正常")
                return True
        except:
            sys.stdout = old_stdout
            print("⚠️ 无法检查迁移状态")
            return False
            
    except Exception as e:
        print(f"❌ 检查迁移状态失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始Django环境测试...")
    print(f"当前目录: {current_dir}")
    print("-" * 50)
    
    tests = [
        ("Django导入", test_django_import),
        ("Django设置", test_django_setup),
        ("模型导入", test_models_import),
        ("数据库连接", test_database_connection),
        ("迁移状态", test_migrations_needed),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"  建议: 检查 {test_name} 相关配置")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Django环境准备就绪")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
