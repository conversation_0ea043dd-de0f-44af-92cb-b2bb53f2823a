{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}RunSim 项目仪表板{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- 自定义样式 -->
    <link href="{% static 'css/dashboard.css' %}" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        }
        .stat-card {
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }
        .error-message {
            color: #dc3545;
            text-align: center;
            padding: 20px;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>RunSim 仪表板
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-tachometer-alt me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/testplan">
                            <i class="fas fa-list-check me-1"></i>用例管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/bug">
                            <i class="fas fa-bug me-1"></i>BUG管理
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text">
                            <i class="fas fa-clock me-1"></i>
                            <span id="current-time"></span>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <small>&copy; 2024 RunSim GUI Dashboard. All rights reserved.</small>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- 仪表板核心脚本 -->
    <script src="{% static 'js/dashboard.js' %}"></script>

    <!-- Error Handler -->
    <script src="{% static 'js/error-handler.js' %}"></script>

    <!-- Common JavaScript -->
    <script>
        // 更新当前时间
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            $('#current-time').text(timeString);
        }

        // 每秒更新时间
        setInterval(updateCurrentTime, 1000);
        updateCurrentTime();

        // 通用错误处理
        function showError(message) {
            console.error(message);
            // 可以在这里添加更多的错误显示逻辑
        }

        // 通用成功提示
        function showSuccess(message) {
            console.log(message);
            // 可以在这里添加更多的成功提示逻辑
        }

        // AJAX错误处理
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            // 过滤掉非本应用的请求错误
            if (settings.url && !settings.url.includes('chatgpt.io') && !settings.url.includes('api.chatgpt.io')) {
                showError('请求失败: ' + thrownError);
            }
        });

        // 控制台错误过滤
        const originalConsoleError = console.error;
        console.error = function(...args) {
            // 过滤掉ChatGPT相关的错误
            const message = args.join(' ');
            if (!message.includes('chatgpt.io') && !message.includes('api.chatgpt.io')) {
                originalConsoleError.apply(console, args);
            }
        };

        // 网络请求监控（仅用于调试）
        if (window.fetch) {
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                const url = args[0];
                if (typeof url === 'string' && (url.includes('chatgpt.io') || url.includes('api.chatgpt.io'))) {
                    console.warn('阻止了对ChatGPT API的请求:', url);
                    return Promise.reject(new Error('Blocked ChatGPT API request'));
                }
                return originalFetch.apply(this, args);
            };
        }
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
