#!/usr/bin/env python
"""
修复Django迁移冲突脚本

解决表已存在的问题
"""

import os
import sys
import sqlite3
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

def setup_django():
    """设置Django环境"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dashboard_project.settings')
    import django
    django.setup()

def find_database_file():
    """查找数据库文件"""
    from django.conf import settings
    db_path = settings.DATABASES['default']['NAME']
    print(f"数据库文件路径: {db_path}")
    return db_path

def check_existing_tables(db_path):
    """检查现有表"""
    if not os.path.exists(db_path):
        print("数据库文件不存在，可以直接执行迁移")
        return []
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = [row[0] for row in cursor.fetchall()]
    
    conn.close()
    
    print(f"现有表: {tables}")
    return tables

def backup_database(db_path):
    """备份数据库"""
    if not os.path.exists(db_path):
        return
    
    backup_path = f"{db_path}.backup"
    import shutil
    shutil.copy2(db_path, backup_path)
    print(f"数据库已备份到: {backup_path}")

def fake_initial_migration():
    """标记初始迁移为已应用"""
    from django.core.management import execute_from_command_line
    
    print("标记初始迁移为已应用...")
    try:
        execute_from_command_line(['manage.py', 'migrate', 'dashboard_app', '--fake-initial'])
        print("✅ 初始迁移标记成功")
        return True
    except Exception as e:
        print(f"❌ 标记初始迁移失败: {e}")
        return False

def create_migration_table():
    """创建Django迁移表"""
    from django.core.management import execute_from_command_line
    
    print("创建Django迁移表...")
    try:
        execute_from_command_line(['manage.py', 'migrate', '--run-syncdb'])
        print("✅ Django迁移表创建成功")
        return True
    except Exception as e:
        print(f"❌ 创建Django迁移表失败: {e}")
        return False

def fix_table_structure():
    """修复表结构差异"""
    db_path = find_database_file()
    
    if not os.path.exists(db_path):
        print("数据库文件不存在，跳过表结构修复")
        return True
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查并添加缺失的列
        print("检查表结构...")
        
        # 检查test_cases表的列
        cursor.execute("PRAGMA table_info(test_cases)")
        columns = [row[1] for row in cursor.fetchall()]
        
        # 需要添加的列
        new_columns = [
            ('post_subsys_phase', 'VARCHAR(50)'),
            ('post_subsys_status', 'VARCHAR(50) DEFAULT "Pending"'),
            ('post_top_phase', 'VARCHAR(50)'),
            ('post_top_status', 'VARCHAR(50) DEFAULT "Pending"'),
            ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
            ('updated_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
        ]
        
        for col_name, col_type in new_columns:
            if col_name not in columns:
                try:
                    cursor.execute(f"ALTER TABLE test_cases ADD COLUMN {col_name} {col_type}")
                    print(f"✅ 添加列: {col_name}")
                except sqlite3.OperationalError as e:
                    if "duplicate column name" not in str(e):
                        print(f"⚠️ 添加列失败: {col_name} - {e}")
        
        # 检查projects表
        cursor.execute("PRAGMA table_info(projects)")
        project_columns = [row[1] for row in cursor.fetchall()]
        
        project_new_columns = [
            ('created_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
            ('updated_at', 'DATETIME DEFAULT CURRENT_TIMESTAMP'),
        ]
        
        for col_name, col_type in project_new_columns:
            if col_name not in project_columns:
                try:
                    cursor.execute(f"ALTER TABLE projects ADD COLUMN {col_name} {col_type}")
                    print(f"✅ 添加列到projects: {col_name}")
                except sqlite3.OperationalError as e:
                    if "duplicate column name" not in str(e):
                        print(f"⚠️ 添加列失败: {col_name} - {e}")
        
        conn.commit()
        print("✅ 表结构修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 表结构修复失败: {e}")
        return False
    finally:
        conn.close()

def main():
    """主函数"""
    print("Django迁移冲突修复工具")
    print("=" * 40)
    
    try:
        # 设置Django环境
        setup_django()
        
        # 查找数据库文件
        db_path = find_database_file()
        
        # 检查现有表
        existing_tables = check_existing_tables(db_path)
        
        if existing_tables:
            print("\n检测到现有数据库表，开始修复...")
            
            # 备份数据库
            backup_database(db_path)
            
            # 修复表结构
            if not fix_table_structure():
                print("❌ 表结构修复失败")
                return False
            
            # 创建Django迁移表
            if not create_migration_table():
                print("❌ 创建Django迁移表失败")
                return False
            
            # 标记初始迁移为已应用
            if not fake_initial_migration():
                print("❌ 标记初始迁移失败")
                return False
        else:
            print("\n数据库为空，可以直接执行正常迁移")
            from django.core.management import execute_from_command_line
            execute_from_command_line(['manage.py', 'migrate'])
        
        print("\n✅ 迁移冲突修复完成！")
        print("\n下一步:")
        print("1. 运行: python manage.py check")
        print("2. 运行: python manage.py runserver 127.0.0.1:5001")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
