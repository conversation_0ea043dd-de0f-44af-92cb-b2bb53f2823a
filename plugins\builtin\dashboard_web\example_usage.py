#!/usr/bin/env python3
"""
RunSim Dashboard 数据库路径配置使用示例

本脚本演示如何使用不同的方法配置数据库路径
"""

import os
import sys

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def example_1_default_usage():
    """示例1: 默认使用方式"""
    print("📋 示例1: 默认使用方式")
    print("-" * 40)
    print("不设置任何配置，系统会自动使用当前工作目录")
    
    from config import get_database_path
    db_path = get_database_path()
    
    print(f"📁 数据库路径: {db_path}")
    print(f"📁 路径说明: 在当前工作目录下创建 runsim_dashboard.db")
    print()

def example_2_environment_variable():
    """示例2: 使用环境变量"""
    print("📋 示例2: 使用环境变量")
    print("-" * 40)
    print("通过设置 RUNSIM_DB_PATH 环境变量来指定数据库路径")
    
    print("Windows 设置方法:")
    print("  临时设置: set RUNSIM_DB_PATH=C:\\Users\\<USER>\\runsim_data\\dashboard.db")
    print("  永久设置: setx RUNSIM_DB_PATH \"C:\\Users\\<USER>\\runsim_data\\dashboard.db\"")
    print()
    
    print("Linux/Mac 设置方法:")
    print("  临时设置: export RUNSIM_DB_PATH=/home/<USER>/runsim_data/dashboard.db")
    print("  永久设置: echo 'export RUNSIM_DB_PATH=/home/<USER>/runsim_data/dashboard.db' >> ~/.bashrc")
    print()

def example_3_config_file():
    """示例3: 使用配置文件"""
    print("📋 示例3: 使用配置文件")
    print("-" * 40)
    print("通过编辑 database_config.json 文件来配置数据库路径")
    
    import json

# 配置UTF-8输出
import sys
import io
if hasattr(sys.stdout, 'buffer'):
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
if hasattr(sys.stderr, 'buffer'):
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

    example_config = {
        "database": {
            "path": "C:/Users/<USER>/runsim_data/dashboard.db",
            "description": "数据库文件路径配置"
        },
        "backup": {
            "enabled": True,
            "max_backups": 10
        }
    }
    
    print("配置文件内容示例:")
    print(json.dumps(example_config, indent=4, ensure_ascii=False))
    print()

def example_4_multi_user_setup():
    """示例4: 多用户环境配置"""
    print("📋 示例4: 多用户环境配置")
    print("-" * 40)
    print("在多用户环境下，每个用户可以有自己的数据库")
    
    print("方案1: 用户个人目录")
    print("  Windows: set RUNSIM_DB_PATH=%USERPROFILE%\\runsim_data\\dashboard.db")
    print("  Linux/Mac: export RUNSIM_DB_PATH=$HOME/runsim_data/dashboard.db")
    print()
    
    print("方案2: 共享目录")
    print("  所有用户使用同一个共享数据库")
    print("  export RUNSIM_DB_PATH=/shared/runsim_data/dashboard.db")
    print()
    
    print("方案3: 项目特定")
    print("  每个项目使用独立的数据库")
    print("  cd /path/to/project")
    print("  export RUNSIM_DB_PATH=./project_dashboard.db")
    print()

def example_5_migration():
    """示例5: 数据迁移"""
    print("📋 示例5: 数据迁移")
    print("-" * 40)
    print("如何将现有数据迁移到新路径")
    
    print("步骤:")
    print("1. 备份现有数据库:")
    print("   cp plugins/builtin/dashboard_web/data/dashboard.db backup_dashboard.db")
    print()
    print("2. 设置新路径:")
    print("   export RUNSIM_DB_PATH=/new/path/dashboard.db")
    print()
    print("3. 复制数据库文件:")
    print("   cp backup_dashboard.db /new/path/dashboard.db")
    print()
    print("4. 验证配置:")
    print("   python configure_database_path.py")
    print()

def example_6_troubleshooting():
    """示例6: 故障排除"""
    print("📋 示例6: 故障排除")
    print("-" * 40)
    print("常见问题及解决方案")
    
    print("问题1: 权限被拒绝")
    print("  症状: 无法创建或写入数据库文件")
    print("  解决: 检查目录权限，使用有权限的目录")
    print()
    
    print("问题2: 数据库文件不存在")
    print("  症状: 仪表板显示空数据")
    print("  解决: 运行配置工具测试数据库访问")
    print()
    
    print("问题3: 配置不生效")
    print("  症状: 仍然使用旧的数据库路径")
    print("  解决: 重启应用程序，检查环境变量设置")
    print()

def main():
    """主函数"""
    print("🛠️ RunSim Dashboard 数据库路径配置使用示例")
    print("=" * 70)
    print()
    
    examples = [
        example_1_default_usage,
        example_2_environment_variable,
        example_3_config_file,
        example_4_multi_user_setup,
        example_5_migration,
        example_6_troubleshooting
    ]
    
    for example in examples:
        example()
    
    print("📚 更多信息请参考:")
    print("  - DATABASE_PATH_GUIDE.md: 详细配置指南")
    print("  - configure_database_path.py: 配置工具")
    print("  - test_database_path.py: 功能测试")
    print()
    
    print("🔧 快速配置:")
    print("  python configure_database_path.py")

if __name__ == '__main__':
    main()
