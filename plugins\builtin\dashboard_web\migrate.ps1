# Django迁移PowerShell脚本
param(
    [switch]$TestOnly,
    [switch]$SkipMigration,
    [switch]$Verbose
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "RunSim GUI Dashboard Django 迁移脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 切换到脚本目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir
Write-Host "当前目录: $PWD" -ForegroundColor Yellow
Write-Host ""

# 函数：执行命令并检查结果
function Invoke-CommandWithCheck {
    param(
        [string]$Command,
        [string]$Description,
        [switch]$ContinueOnError
    )
    
    Write-Host "执行: $Description" -ForegroundColor Green
    Write-Host "命令: $Command" -ForegroundColor Gray
    
    try {
        $result = Invoke-Expression $Command
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $Description 成功" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ $Description 失败 (退出码: $LASTEXITCODE)" -ForegroundColor Red
            if (-not $ContinueOnError) {
                return $false
            }
        }
    } catch {
        Write-Host "❌ $Description 异常: $($_.Exception.Message)" -ForegroundColor Red
        if (-not $ContinueOnError) {
            return $false
        }
    }
    return $true
}

# 步骤1: 测试Django环境
Write-Host "步骤 1: 测试Django环境" -ForegroundColor Cyan
Write-Host "----------------------------------------" -ForegroundColor Cyan

if (-not (Invoke-CommandWithCheck "python test_django.py" "Django环境测试")) {
    Write-Host "❌ Django环境测试失败！请检查Django是否正确安装" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

if ($TestOnly) {
    Write-Host "🧪 仅测试模式，跳过实际迁移" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 0
}

# 步骤2: 生成数据库迁移文件
Write-Host "`n步骤 2: 生成数据库迁移文件" -ForegroundColor Cyan
Write-Host "----------------------------------------" -ForegroundColor Cyan

if (-not (Invoke-CommandWithCheck "python manage.py makemigrations dashboard_app" "生成迁移文件")) {
    Write-Host "❌ 生成迁移文件失败！" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

# 步骤3: 执行数据库迁移
Write-Host "`n步骤 3: 执行数据库迁移" -ForegroundColor Cyan
Write-Host "----------------------------------------" -ForegroundColor Cyan

if (-not (Invoke-CommandWithCheck "python manage.py migrate" "执行数据库迁移")) {
    Write-Host "❌ 数据库迁移失败！" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

# 步骤4: Django系统检查
Write-Host "`n步骤 4: Django系统检查" -ForegroundColor Cyan
Write-Host "----------------------------------------" -ForegroundColor Cyan

Invoke-CommandWithCheck "python manage.py check" "Django系统检查" -ContinueOnError

# 步骤5: 迁移Flask数据 (可选)
if (-not $SkipMigration) {
    Write-Host "`n步骤 5: 迁移Flask数据" -ForegroundColor Cyan
    Write-Host "----------------------------------------" -ForegroundColor Cyan
    
    $migrateData = Read-Host "是否迁移现有Flask数据？(y/N)"
    if ($migrateData -eq "y" -or $migrateData -eq "Y") {
        Write-Host "开始迁移Flask数据..." -ForegroundColor Yellow
        if (Invoke-CommandWithCheck "python manage.py migrate_flask_data" "Flask数据迁移" -ContinueOnError) {
            Write-Host "✅ 数据迁移完成" -ForegroundColor Green
        } else {
            Write-Host "⚠️ 数据迁移失败，但Django环境已准备就绪" -ForegroundColor Yellow
        }
    } else {
        Write-Host "跳过数据迁移" -ForegroundColor Yellow
    }
}

# 步骤6: 收集静态文件
Write-Host "`n步骤 6: 收集静态文件" -ForegroundColor Cyan
Write-Host "----------------------------------------" -ForegroundColor Cyan

Invoke-CommandWithCheck "python manage.py collectstatic --noinput" "收集静态文件" -ContinueOnError

# 步骤7: 测试Django服务器
Write-Host "`n步骤 7: 测试Django服务器" -ForegroundColor Cyan
Write-Host "----------------------------------------" -ForegroundColor Cyan

Write-Host "启动测试服务器 (将在5秒后自动停止)..." -ForegroundColor Yellow

# 启动服务器进程
$serverJob = Start-Job -ScriptBlock {
    param($workingDir)
    Set-Location $workingDir
    python manage.py runserver 127.0.0.1:5001 --noreload
} -ArgumentList $PWD

# 等待服务器启动
Start-Sleep -Seconds 3

# 测试连接
try {
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $tcpClient.Connect("127.0.0.1", 5001)
    $tcpClient.Close()
    Write-Host "✅ 服务器连接成功" -ForegroundColor Green
    $serverTest = $true
} catch {
    Write-Host "❌ 无法连接到服务器" -ForegroundColor Red
    $serverTest = $false
}

# 停止服务器
Stop-Job $serverJob -Force
Remove-Job $serverJob -Force
Write-Host "✅ 测试服务器已停止" -ForegroundColor Green

# 输出结果
Write-Host "`n========================================" -ForegroundColor Cyan
if ($serverTest) {
    Write-Host "🎉 Django迁移完成！" -ForegroundColor Green
} else {
    Write-Host "⚠️ Django迁移基本完成，但服务器测试失败" -ForegroundColor Yellow
}
Write-Host "========================================" -ForegroundColor Cyan

Write-Host "`n📋 下一步操作:" -ForegroundColor Yellow
Write-Host "1. 启动RunSim GUI" -ForegroundColor White
Write-Host "2. 测试仪表板插件" -ForegroundColor White
Write-Host "3. 验证所有功能正常" -ForegroundColor White
Write-Host ""
Write-Host "如需手动启动服务器，运行:" -ForegroundColor Yellow
Write-Host "python manage.py runserver 127.0.0.1:5001" -ForegroundColor White
Write-Host ""

Read-Host "按回车键退出"
