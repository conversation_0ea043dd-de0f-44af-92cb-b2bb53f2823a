#!/usr/bin/env python3
"""
性能优化脚本

该脚本用于优化RunSim GUI仪表板的性能，包括：
1. 数据库优化
2. 内存管理优化
3. 缓存机制优化
4. 静态资源优化
"""

import os
import sys
import sqlite3
import time
import gc
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        self.db_path = os.path.join(current_dir, 'data', 'dashboard.db')
        self.optimization_results = {}
        
    def run_all_optimizations(self):
        """运行所有优化"""
        print("=" * 60)
        print("RunSim GUI 仪表板 - 性能优化")
        print("=" * 60)
        print(f"优化开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        try:
            # 1. 数据库优化
            print("1. 数据库优化...")
            self._optimize_database()
            
            # 2. 内存管理优化
            print("\n2. 内存管理优化...")
            self._optimize_memory()
            
            # 3. 缓存机制优化
            print("\n3. 缓存机制优化...")
            self._optimize_cache()
            
            # 4. 静态资源优化
            print("\n4. 静态资源优化...")
            self._optimize_static_resources()
            
            # 5. 生成优化报告
            print("\n5. 生成优化报告...")
            self._generate_optimization_report()
            
            print("\n✅ 性能优化完成！")
            return True
            
        except Exception as e:
            print(f"\n❌ 性能优化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _optimize_database(self):
        """优化数据库"""
        try:
            if not os.path.exists(self.db_path):
                print("  ⚠️ 数据库文件不存在，跳过数据库优化")
                return
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 1. 分析数据库
                print("  1.1 分析数据库...")
                cursor.execute("ANALYZE")
                
                # 2. 重建索引
                print("  1.2 重建索引...")
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_test_cases_name ON test_cases(case_name)",
                    "CREATE INDEX IF NOT EXISTS idx_test_cases_project ON test_cases(project_id)",
                    "CREATE INDEX IF NOT EXISTS idx_test_cases_status ON test_cases(subsys_status, top_status)",
                    "CREATE INDEX IF NOT EXISTS idx_bugs_project ON bugs(project_id)",
                    "CREATE INDEX IF NOT EXISTS idx_bugs_status ON bugs(status)",
                    "CREATE INDEX IF NOT EXISTS idx_bugs_severity ON bugs(severity)",
                    "CREATE INDEX IF NOT EXISTS idx_case_history_case ON case_status_history(case_id)",
                    "CREATE INDEX IF NOT EXISTS idx_case_history_time ON case_status_history(changed_at)"
                ]
                
                for index_sql in indexes:
                    cursor.execute(index_sql)
                
                # 3. 优化数据库设置
                print("  1.3 优化数据库设置...")
                optimizations = [
                    "PRAGMA journal_mode=WAL",
                    "PRAGMA synchronous=NORMAL",
                    "PRAGMA cache_size=10000",
                    "PRAGMA temp_store=MEMORY",
                    "PRAGMA mmap_size=268435456",  # 256MB
                    "PRAGMA optimize"
                ]
                
                for pragma in optimizations:
                    cursor.execute(pragma)
                
                # 4. 清理数据库
                print("  1.4 清理数据库...")
                cursor.execute("VACUUM")
                
                conn.commit()
                
            # 5. 检查优化效果
            print("  1.5 检查优化效果...")
            start_time = time.time()
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM test_cases")
                cursor.execute("SELECT COUNT(*) FROM bugs")
                cursor.execute("SELECT COUNT(*) FROM projects")
            end_time = time.time()
            
            query_time = end_time - start_time
            print(f"    查询性能测试: {query_time:.3f}s")
            
            self.optimization_results['database'] = {
                'optimized': True,
                'query_time': query_time,
                'improvements': ['索引重建', '数据库设置优化', '数据库清理']
            }
            
            print("  ✅ 数据库优化完成")
            
        except Exception as e:
            print(f"  ❌ 数据库优化失败: {e}")
            self.optimization_results['database'] = {
                'optimized': False,
                'error': str(e)
            }
    
    def _optimize_memory(self):
        """优化内存管理"""
        try:
            # 1. 获取当前内存使用
            print("  2.1 检查内存使用...")
            try:
                import psutil
                process = psutil.Process()
                memory_before = process.memory_info().rss / 1024 / 1024
                print(f"    优化前内存使用: {memory_before:.1f}MB")
            except ImportError:
                print("    ⚠️ 无法获取内存信息（缺少psutil库）")
                memory_before = 0
            
            # 2. 强制垃圾回收
            print("  2.2 执行垃圾回收...")
            collected = gc.collect()
            print(f"    回收对象数: {collected}")
            
            # 3. 优化Python设置
            print("  2.3 优化Python设置...")
            # 设置垃圾回收阈值
            gc.set_threshold(700, 10, 10)
            
            # 4. 检查优化效果
            if memory_before > 0:
                try:
                    memory_after = process.memory_info().rss / 1024 / 1024
                    memory_saved = memory_before - memory_after
                    print(f"    优化后内存使用: {memory_after:.1f}MB")
                    print(f"    节省内存: {memory_saved:.1f}MB")
                except:
                    memory_after = memory_before
                    memory_saved = 0
            else:
                memory_after = 0
                memory_saved = 0
            
            self.optimization_results['memory'] = {
                'optimized': True,
                'memory_before': memory_before,
                'memory_after': memory_after,
                'memory_saved': memory_saved,
                'objects_collected': collected
            }
            
            print("  ✅ 内存优化完成")
            
        except Exception as e:
            print(f"  ❌ 内存优化失败: {e}")
            self.optimization_results['memory'] = {
                'optimized': False,
                'error': str(e)
            }
    
    def _optimize_cache(self):
        """优化缓存机制"""
        try:
            print("  3.1 创建缓存目录...")
            cache_dir = os.path.join(current_dir, 'cache')
            os.makedirs(cache_dir, exist_ok=True)
            
            print("  3.2 清理过期缓存...")
            cache_files_cleaned = 0
            if os.path.exists(cache_dir):
                current_time = time.time()
                for filename in os.listdir(cache_dir):
                    file_path = os.path.join(cache_dir, filename)
                    if os.path.isfile(file_path):
                        # 删除超过1小时的缓存文件
                        if current_time - os.path.getmtime(file_path) > 3600:
                            os.remove(file_path)
                            cache_files_cleaned += 1
            
            print(f"    清理缓存文件: {cache_files_cleaned}个")
            
            print("  3.3 创建缓存配置...")
            cache_config = {
                'enabled': True,
                'max_size': '100MB',
                'ttl': 3600,  # 1小时
                'cleanup_interval': 1800  # 30分钟
            }
            
            # 保存缓存配置
            import json
            config_file = os.path.join(cache_dir, 'cache_config.json')
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(cache_config, f, indent=2)
            
            self.optimization_results['cache'] = {
                'optimized': True,
                'cache_dir_created': True,
                'files_cleaned': cache_files_cleaned,
                'config_created': True
            }
            
            print("  ✅ 缓存优化完成")
            
        except Exception as e:
            print(f"  ❌ 缓存优化失败: {e}")
            self.optimization_results['cache'] = {
                'optimized': False,
                'error': str(e)
            }
    
    def _optimize_static_resources(self):
        """优化静态资源"""
        try:
            static_dir = os.path.join(current_dir, 'static')
            
            print("  4.1 检查静态资源目录...")
            if not os.path.exists(static_dir):
                print("    创建静态资源目录...")
                os.makedirs(static_dir, exist_ok=True)
                os.makedirs(os.path.join(static_dir, 'css'), exist_ok=True)
                os.makedirs(os.path.join(static_dir, 'js'), exist_ok=True)
                os.makedirs(os.path.join(static_dir, 'uploads'), exist_ok=True)
            
            print("  4.2 检查必要的静态文件...")
            required_files = {
                'css/bootstrap.min.css': 'Bootstrap CSS框架',
                'js/bootstrap.min.js': 'Bootstrap JavaScript',
                'js/jquery.min.js': 'jQuery库',
                'js/chart.min.js': 'Chart.js图表库'
            }
            
            missing_files = []
            for file_path, description in required_files.items():
                full_path = os.path.join(static_dir, file_path)
                if not os.path.exists(full_path):
                    missing_files.append((file_path, description))
                    print(f"    ⚠️ 缺少文件: {file_path} ({description})")
                else:
                    print(f"    ✓ {file_path}")
            
            print("  4.3 创建资源优化配置...")
            optimization_config = {
                'gzip_enabled': True,
                'cache_control': 'max-age=3600',
                'cdn_fallback': True,
                'missing_files': missing_files
            }
            
            # 保存优化配置
            import json
            config_file = os.path.join(static_dir, 'optimization_config.json')
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(optimization_config, f, indent=2)
            
            self.optimization_results['static_resources'] = {
                'optimized': True,
                'directories_created': True,
                'missing_files_count': len(missing_files),
                'config_created': True
            }
            
            print("  ✅ 静态资源优化完成")
            
        except Exception as e:
            print(f"  ❌ 静态资源优化失败: {e}")
            self.optimization_results['static_resources'] = {
                'optimized': False,
                'error': str(e)
            }
    
    def _generate_optimization_report(self):
        """生成优化报告"""
        try:
            report = {
                'optimization_time': datetime.now().isoformat(),
                'results': self.optimization_results,
                'summary': self._generate_summary()
            }
            
            # 保存报告
            report_file = f'performance_optimization_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            import json

# 配置UTF-8输出
import sys
import io
if hasattr(sys.stdout, 'buffer'):
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
if hasattr(sys.stderr, 'buffer'):
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"  优化报告已保存: {report_file}")
            
            # 显示摘要
            self._display_summary()
            
        except Exception as e:
            print(f"  ❌ 生成优化报告失败: {e}")
    
    def _generate_summary(self):
        """生成优化摘要"""
        summary = {
            'total_optimizations': 0,
            'successful_optimizations': 0,
            'failed_optimizations': 0,
            'improvements': []
        }
        
        for category, result in self.optimization_results.items():
            summary['total_optimizations'] += 1
            if result.get('optimized', False):
                summary['successful_optimizations'] += 1
                
                # 添加具体改进
                if category == 'database' and 'query_time' in result:
                    summary['improvements'].append(f"数据库查询时间: {result['query_time']:.3f}s")
                
                if category == 'memory' and 'memory_saved' in result:
                    if result['memory_saved'] > 0:
                        summary['improvements'].append(f"节省内存: {result['memory_saved']:.1f}MB")
                
                if category == 'cache' and 'files_cleaned' in result:
                    if result['files_cleaned'] > 0:
                        summary['improvements'].append(f"清理缓存文件: {result['files_cleaned']}个")
                        
            else:
                summary['failed_optimizations'] += 1
        
        return summary
    
    def _display_summary(self):
        """显示优化摘要"""
        print("\n" + "=" * 50)
        print("优化摘要")
        print("=" * 50)
        
        summary = self._generate_summary()
        
        print(f"总优化项: {summary['total_optimizations']}")
        print(f"成功优化: {summary['successful_optimizations']}")
        print(f"失败优化: {summary['failed_optimizations']}")
        print(f"成功率: {summary['successful_optimizations']/summary['total_optimizations']*100:.1f}%")
        
        if summary['improvements']:
            print("\n具体改进:")
            for improvement in summary['improvements']:
                print(f"  • {improvement}")
        
        print("\n各模块优化状态:")
        for category, result in self.optimization_results.items():
            status = "✅" if result.get('optimized', False) else "❌"
            print(f"  {status} {category.replace('_', ' ').title()}")

def main():
    """主函数"""
    optimizer = PerformanceOptimizer()
    success = optimizer.run_all_optimizations()
    
    if success:
        print("\n🎉 性能优化完成！")
        return 0
    else:
        print("\n❌ 性能优化失败")
        return 1

if __name__ == '__main__':
    sys.exit(main())
