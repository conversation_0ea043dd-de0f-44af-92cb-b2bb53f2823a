"""
Django models for RunSim Dashboard

这些模型基于原有的SQLite数据库结构创建，保持数据兼容性。
"""

from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator


class Project(models.Model):
    """项目模型"""
    name = models.CharField(max_length=255, verbose_name='项目名称')
    subsystem = models.CharField(max_length=255, blank=True, null=True, verbose_name='子系统')
    description = models.TextField(blank=True, null=True, verbose_name='项目描述')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'projects'
        verbose_name = '项目'
        verbose_name_plural = '项目'
        ordering = ['-created_at']

    def __str__(self):
        return self.name


class TestCase(models.Model):
    """测试用例模型"""
    
    # 状态选择
    STATUS_CHOICES = [
        ('PASS', 'PASS'),
        ('Pending', 'Pending'),
        ('On-Going', 'On-Going'),
        ('N/A', 'N/A'),
    ]
    
    # 验证阶段选择
    PHASE_CHOICES = [
        ('DVR1', 'DVR1'),
        ('DVR2', 'DVR2'),
        ('DVR3', 'DVR3'),
        ('DVS1', 'DVS1'),
        ('DVS2', 'DVS2'),
        ('N/A', 'N/A'),
    ]

    project = models.ForeignKey(Project, on_delete=models.CASCADE, verbose_name='项目')
    category = models.CharField(max_length=255, blank=True, null=True, verbose_name='分类')
    number = models.CharField(max_length=255, blank=True, null=True, verbose_name='编号')
    test_areas = models.CharField(max_length=255, blank=True, null=True, verbose_name='测试区域')
    test_scope = models.CharField(max_length=255, blank=True, null=True, verbose_name='测试范围')
    function_point = models.CharField(max_length=255, blank=True, null=True, verbose_name='功能点')
    check_point = models.CharField(max_length=255, blank=True, null=True, verbose_name='检查点')
    cover = models.CharField(max_length=255, blank=True, null=True, verbose_name='覆盖')
    case_name = models.CharField(max_length=255, verbose_name='用例名称')
    
    # 时间字段
    start_time = models.DateTimeField(blank=True, null=True, verbose_name='开始时间')
    end_time = models.DateTimeField(blank=True, null=True, verbose_name='结束时间')
    actual_time = models.IntegerField(blank=True, null=True, verbose_name='实际耗时(秒)')
    
    owner = models.CharField(max_length=255, blank=True, null=True, verbose_name='负责人')
    
    # 验证阶段字段
    subsys_phase = models.CharField(max_length=50, choices=PHASE_CHOICES, blank=True, null=True, verbose_name='子系统阶段')
    subsys_status = models.CharField(max_length=50, choices=STATUS_CHOICES, default='Pending', verbose_name='子系统状态')
    top_phase = models.CharField(max_length=50, choices=PHASE_CHOICES, blank=True, null=True, verbose_name='TOP阶段')
    top_status = models.CharField(max_length=50, choices=STATUS_CHOICES, default='Pending', verbose_name='TOP状态')
    
    # 后仿标记字段
    post_subsys_phase = models.CharField(max_length=50, blank=True, null=True, verbose_name='后仿子系统阶段')
    post_subsys_status = models.CharField(max_length=50, choices=STATUS_CHOICES, default='Pending', verbose_name='后仿子系统状态')
    post_top_phase = models.CharField(max_length=50, blank=True, null=True, verbose_name='后仿TOP阶段')
    post_top_status = models.CharField(max_length=50, choices=STATUS_CHOICES, default='Pending', verbose_name='后仿TOP状态')
    
    remarks = models.TextField(blank=True, null=True, verbose_name='备注')
    
    # 兼容旧格式字段
    subsys_stage = models.CharField(max_length=50, blank=True, null=True, verbose_name='子系统阶段(旧)')
    top_stage = models.CharField(max_length=50, blank=True, null=True, verbose_name='TOP阶段(旧)')
    test_process = models.CharField(max_length=255, blank=True, null=True, verbose_name='测试流程')
    coverage_point = models.CharField(max_length=255, blank=True, null=True, verbose_name='覆盖点')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'test_cases'
        verbose_name = '测试用例'
        verbose_name_plural = '测试用例'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['case_name']),
            models.Index(fields=['project']),
        ]

    def __str__(self):
        return self.case_name

    @property
    def duration(self):
        """计算执行时长"""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return self.actual_time


class Bug(models.Model):
    """BUG模型"""
    
    SEVERITY_CHOICES = [
        ('Low', 'Low'),
        ('Medium', 'Medium'),
        ('High', 'High'),
        ('Critical', 'Critical'),
    ]
    
    STATUS_CHOICES = [
        ('Open', 'Open'),
        ('In Progress', 'In Progress'),
        ('Fixed', 'Fixed'),
        ('Closed', 'Closed'),
        ('Rejected', 'Rejected'),
    ]

    project = models.ForeignKey(Project, on_delete=models.CASCADE, verbose_name='项目')
    bug_id = models.CharField(max_length=255, verbose_name='BUG ID')
    bug_type = models.CharField(max_length=255, blank=True, null=True, verbose_name='BUG类型')
    submit_sys = models.CharField(max_length=255, blank=True, null=True, verbose_name='提交系统')
    verification_stage = models.CharField(max_length=255, blank=True, null=True, verbose_name='验证阶段')
    description = models.TextField(blank=True, null=True, verbose_name='描述')
    discovery_platform = models.CharField(max_length=255, blank=True, null=True, verbose_name='发现平台')
    discovery_case = models.CharField(max_length=255, blank=True, null=True, verbose_name='发现用例')
    severity = models.CharField(max_length=50, choices=SEVERITY_CHOICES, default='Medium', verbose_name='严重程度')
    status = models.CharField(max_length=50, choices=STATUS_CHOICES, default='Open', verbose_name='状态')
    submitter = models.CharField(max_length=255, blank=True, null=True, verbose_name='提交人')
    verifier = models.CharField(max_length=255, blank=True, null=True, verbose_name='验证人')
    submit_date = models.DateField(blank=True, null=True, verbose_name='提交日期')
    fix_date = models.DateField(blank=True, null=True, verbose_name='修复日期')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'bugs'
        verbose_name = 'BUG'
        verbose_name_plural = 'BUG'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['project']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"{self.bug_id} - {self.description[:50] if self.description else ''}"


class CaseStatusHistory(models.Model):
    """用例状态历史模型"""
    
    STAGE_TYPE_CHOICES = [
        ('subsys', 'subsys'),
        ('top', 'top'),
        ('post_subsys', 'post_subsys'),
        ('post_top', 'post_top'),
    ]

    case = models.ForeignKey(TestCase, on_delete=models.CASCADE, verbose_name='测试用例')
    old_status = models.CharField(max_length=50, blank=True, null=True, verbose_name='旧状态')
    new_status = models.CharField(max_length=50, blank=True, null=True, verbose_name='新状态')
    stage_type = models.CharField(max_length=50, choices=STAGE_TYPE_CHOICES, verbose_name='阶段类型')
    changed_by = models.CharField(max_length=255, blank=True, null=True, verbose_name='修改人')
    changed_at = models.DateTimeField(auto_now_add=True, verbose_name='修改时间')

    class Meta:
        db_table = 'case_status_history'
        verbose_name = '用例状态历史'
        verbose_name_plural = '用例状态历史'
        ordering = ['-changed_at']
        indexes = [
            models.Index(fields=['case']),
        ]

    def __str__(self):
        return f"{self.case.case_name} - {self.stage_type}: {self.old_status} -> {self.new_status}"


class SystemConfig(models.Model):
    """系统配置模型"""
    
    config_key = models.CharField(max_length=255, unique=True, verbose_name='配置键')
    config_value = models.TextField(blank=True, null=True, verbose_name='配置值')
    description = models.TextField(blank=True, null=True, verbose_name='描述')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'system_config'
        verbose_name = '系统配置'
        verbose_name_plural = '系统配置'
        ordering = ['config_key']

    def __str__(self):
        return f"{self.config_key}: {self.config_value}"


class PhaseStatistics(models.Model):
    """验证阶段统计模型"""
    
    PHASE_CHOICES = [
        ('DVR1', 'DVR1'),
        ('DVR2', 'DVR2'),
        ('DVR3', 'DVR3'),
        ('DVS1', 'DVS1'),
        ('DVS2', 'DVS2'),
    ]
    
    CASE_TYPE_CHOICES = [
        ('subsys', 'subsys'),
        ('top', 'top'),
        ('post_subsys', 'post_subsys'),
        ('post_top', 'post_top'),
    ]

    project = models.ForeignKey(Project, on_delete=models.CASCADE, verbose_name='项目')
    phase_name = models.CharField(max_length=50, choices=PHASE_CHOICES, verbose_name='阶段名称')
    case_type = models.CharField(max_length=50, choices=CASE_TYPE_CHOICES, verbose_name='用例类型')
    total_cases = models.IntegerField(default=0, verbose_name='总用例数')
    pass_cases = models.IntegerField(default=0, verbose_name='通过用例数')
    fail_cases = models.IntegerField(default=0, verbose_name='失败用例数')
    ongoing_cases = models.IntegerField(default=0, verbose_name='进行中用例数')
    not_started_cases = models.IntegerField(default=0, verbose_name='未开始用例数')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'phase_statistics'
        verbose_name = '阶段统计'
        verbose_name_plural = '阶段统计'
        unique_together = ['project', 'phase_name', 'case_type']
        ordering = ['project', 'phase_name', 'case_type']

    def __str__(self):
        return f"{self.project.name} - {self.phase_name} - {self.case_type}"

    @property
    def pass_rate(self):
        """计算通过率"""
        if self.total_cases > 0:
            return (self.pass_cases / self.total_cases) * 100
        return 0
