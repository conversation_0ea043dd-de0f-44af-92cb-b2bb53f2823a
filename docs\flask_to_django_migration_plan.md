# RunSim GUI 仪表板 Flask 到 Django 迁移计划

## 1. 迁移概述

### 1.1 迁移背景
- **当前状态**: 基于Flask框架的Web仪表板
- **迁移原因**: 内网环境不支持Flask框架，但支持Django框架
- **迁移目标**: 完全迁移到Django框架，保持所有现有功能

### 1.2 技术栈对比
| 组件 | Flask实现 | Django实现 |
|------|-----------|------------|
| Web框架 | Flask | Django |
| 模板引擎 | Jinja2 | Django Templates |
| 路由系统 | Flask Blueprint | Django URLs |
| 数据库ORM | 原生SQL + SQLite | Django ORM + SQLite |
| 静态文件 | Flask static | Django static |
| 配置管理 | Python配置类 | Django settings |

## 2. 现有架构分析

### 2.1 Flask应用结构
```
plugins/builtin/dashboard_web/
├── app.py                 # Flask应用主文件
├── config.py              # 配置管理
├── models/
│   ├── database.py        # 数据库操作
│   ├── testplan.py        # 测试计划模型
│   └── bug.py             # BUG模型
├── routes/
│   ├── api.py             # API路由
│   ├── testplan.py        # 测试计划路由
│   ├── bug.py             # BUG管理路由
│   └── export.py          # 数据导出路由
├── templates/             # Jinja2模板
├── static/                # 静态文件
└── utils/                 # 工具模块
```

### 2.2 核心功能模块
1. **测试计划管理**: Excel导入/导出、用例状态跟踪
2. **BUG管理系统**: BUG记录、统计、趋势分析
3. **仪表板可视化**: 统计图表、进度概览
4. **数据库交互**: SQLite数据存储
5. **API接口**: RESTful API服务

## 3. Django目标架构

### 3.1 Django项目结构
```
plugins/builtin/dashboard_web/
├── manage.py              # Django管理脚本
├── dashboard_project/     # Django项目配置
│   ├── __init__.py
│   ├── settings.py        # Django设置
│   ├── urls.py            # 主URL配置
│   └── wsgi.py            # WSGI配置
├── dashboard_app/         # Django应用
│   ├── __init__.py
│   ├── models.py          # Django模型
│   ├── views.py           # 视图函数
│   ├── urls.py            # 应用URL配置
│   ├── admin.py           # 管理界面
│   ├── apps.py            # 应用配置
│   └── migrations/        # 数据库迁移
├── templates/             # Django模板
├── static/                # 静态文件
└── utils/                 # 工具模块
```

### 3.2 Django应用设计
- **dashboard_app**: 主应用，包含所有业务逻辑
- **models**: 使用Django ORM重写数据模型
- **views**: 基于Django视图重写业务逻辑
- **urls**: Django URL路由配置
- **templates**: 转换为Django模板语法

## 4. 详细迁移计划

### 4.1 阶段一：Django项目初始化 (1天)
**目标**: 创建Django项目基础结构

**任务清单**:
- [ ] 创建Django项目和应用
- [ ] 配置Django settings
- [ ] 设置URL路由结构
- [ ] 配置静态文件处理
- [ ] 创建基础模板结构

**关键文件**:
- `manage.py`
- `dashboard_project/settings.py`
- `dashboard_project/urls.py`
- `dashboard_app/apps.py`

### 4.2 阶段二：数据模型迁移 (1天)
**目标**: 将SQLite数据库操作转换为Django ORM

**任务清单**:
- [ ] 分析现有数据库表结构
- [ ] 创建Django模型类
- [ ] 配置数据库连接
- [ ] 生成并执行数据库迁移
- [ ] 数据迁移脚本

**关键文件**:
- `dashboard_app/models.py`
- `dashboard_app/migrations/`

### 4.3 阶段三：视图和API迁移 (2天)
**目标**: 将Flask路由和视图转换为Django视图

**任务清单**:
- [ ] 迁移API视图 (routes/api.py)
- [ ] 迁移测试计划视图 (routes/testplan.py)
- [ ] 迁移BUG管理视图 (routes/bug.py)
- [ ] 迁移数据导出视图 (routes/export.py)
- [ ] 配置URL路由

**关键文件**:
- `dashboard_app/views.py`
- `dashboard_app/urls.py`

### 4.4 阶段四：模板迁移 (1天)
**目标**: 将Jinja2模板转换为Django模板

**任务清单**:
- [ ] 转换base.html模板
- [ ] 转换dashboard.html模板
- [ ] 转换testplan.html模板
- [ ] 转换bug.html模板
- [ ] 更新模板语法和标签

**关键文件**:
- `templates/base.html`
- `templates/dashboard.html`
- `templates/testplan.html`
- `templates/bug.html`

### 4.5 阶段五：集成和测试 (1天)
**目标**: 集成到RunSim GUI并测试功能

**任务清单**:
- [ ] 更新插件启动逻辑
- [ ] 测试所有功能模块
- [ ] 性能优化
- [ ] 错误处理完善
- [ ] 文档更新

**关键文件**:
- `plugins/builtin/dashboard_plugin.py`

## 5. 技术难点和解决方案

### 5.1 数据库迁移
**难点**: 保持现有数据完整性
**解决方案**: 
- 使用Django的数据迁移功能
- 创建数据导入脚本
- 保留原有数据库作为备份

### 5.2 模板语法转换
**难点**: Jinja2到Django模板语法差异
**解决方案**:
- 系统性替换模板标签
- 保持前端JavaScript逻辑不变
- 测试所有页面渲染

### 5.3 URL路由重构
**难点**: Flask Blueprint到Django URL配置
**解决方案**:
- 保持API端点路径不变
- 使用Django的include()组织URL
- 维护向后兼容性

### 5.4 插件集成
**难点**: 与RunSim GUI的集成方式
**解决方案**:
- 保持插件接口不变
- 使用Django的WSGI应用
- 维护相同的启动和停止逻辑

## 6. 风险评估和缓解措施

### 6.1 高风险项
1. **数据丢失风险**
   - 缓解措施: 完整数据备份，分步迁移验证
2. **功能缺失风险**
   - 缓解措施: 详细功能对比测试，用户验收测试
3. **性能下降风险**
   - 缓解措施: 性能基准测试，优化查询和缓存

### 6.2 中风险项
1. **集成兼容性问题**
   - 缓解措施: 保持接口一致性，渐进式替换
2. **用户界面变化**
   - 缓解措施: 保持UI/UX一致性，用户培训

## 7. 验收标准

### 7.1 功能验收
- [ ] 所有现有功能正常工作
- [ ] 数据完整性保持
- [ ] API接口兼容性
- [ ] 用户界面一致性

### 7.2 性能验收
- [ ] 页面加载时间不超过原有系统
- [ ] 数据库查询性能不下降
- [ ] 内存使用合理

### 7.3 集成验收
- [ ] RunSim GUI集成正常
- [ ] 插件启动/停止正常
- [ ] 错误处理完善

## 8. 时间安排

**总预计时间**: 6个工作日

| 阶段 | 时间 | 关键里程碑 |
|------|------|------------|
| 阶段一 | 第1天 | Django项目创建完成 |
| 阶段二 | 第2天 | 数据模型迁移完成 |
| 阶段三 | 第3-4天 | 视图和API迁移完成 |
| 阶段四 | 第5天 | 模板迁移完成 |
| 阶段五 | 第6天 | 集成测试完成 |

## 9. 后续维护

### 9.1 文档更新
- 更新开发文档
- 更新用户手册
- 更新部署指南

### 9.2 培训计划
- 开发团队Django培训
- 用户操作培训
- 故障排除指南

---

**注意**: 本迁移计划将确保在保持所有现有功能的前提下，成功将Flask应用迁移到Django框架。
