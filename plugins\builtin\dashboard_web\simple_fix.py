#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的Django修复脚本 - 避免Unicode字符问题
"""

import os
import sys
import sqlite3
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

def setup_django():
    """设置Django环境"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dashboard_project.settings')
    import django
    django.setup()

def fix_database_migration():
    """修复数据库迁移问题"""
    print("修复数据库迁移...")
    
    try:
        setup_django()
        from django.conf import settings
        
        db_path = settings.DATABASES['default']['NAME']
        print(f"数据库路径: {db_path}")
        
        if os.path.exists(db_path):
            print("检测到现有数据库，标记初始迁移为已应用...")
            
            # 备份数据库
            backup_path = f"{db_path}.backup"
            import shutil
            shutil.copy2(db_path, backup_path)
            print(f"数据库已备份到: {backup_path}")
            
            # 标记初始迁移为已应用
            from django.core.management import execute_from_command_line
            try:
                execute_from_command_line(['manage.py', 'migrate', 'dashboard_app', '--fake-initial'])
                print("初始迁移标记成功")
                return True
            except Exception as e:
                print(f"标记初始迁移失败: {e}")
                return False
        else:
            print("数据库不存在，执行正常迁移...")
            from django.core.management import execute_from_command_line
            execute_from_command_line(['manage.py', 'migrate'])
            print("数据库迁移完成")
            return True
            
    except Exception as e:
        print(f"数据库修复失败: {e}")
        return False

def create_static_dirs():
    """创建静态文件目录"""
    print("创建静态文件目录...")
    
    try:
        static_dir = current_dir / 'static'
        static_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        (static_dir / 'css').mkdir(exist_ok=True)
        (static_dir / 'js').mkdir(exist_ok=True)
        (static_dir / 'img').mkdir(exist_ok=True)
        
        # 创建基础CSS文件
        css_file = static_dir / 'css' / 'dashboard.css'
        if not css_file.exists():
            with open(css_file, 'w', encoding='utf-8') as f:
                f.write("""
/* RunSim Dashboard CSS */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.metric-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}
""")
        
        # 创建基础JS文件
        js_file = static_dir / 'js' / 'dashboard.js'
        if not js_file.exists():
            with open(js_file, 'w', encoding='utf-8') as f:
                f.write("""
// RunSim Dashboard JavaScript
console.log('Dashboard JS loaded');

// 通用函数
function showMessage(message, type = 'info') {
    console.log(`[${type.toUpperCase()}] ${message}`);
}

function formatDateTime(date) {
    return new Date(date).toLocaleString('zh-CN');
}
""")
        
        # 创建错误处理JS文件
        error_js_file = static_dir / 'js' / 'error-handler.js'
        if not error_js_file.exists():
            with open(error_js_file, 'w', encoding='utf-8') as f:
                f.write("""
// Error Handler JavaScript
console.log('Error handler loaded');

window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
});
""")
        
        print("静态文件目录和基础文件创建完成")
        return True
        
    except Exception as e:
        print(f"创建静态文件目录失败: {e}")
        return False

def test_django_server():
    """测试Django服务器"""
    print("测试Django服务器启动...")
    
    try:
        import subprocess
        import time
        
        # 启动服务器进程
        process = subprocess.Popen(
            [sys.executable, 'manage.py', 'runserver', '127.0.0.1:5001', '--noreload'],
            cwd=current_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待服务器启动
        time.sleep(3)
        
        # 检查服务器是否运行
        import socket
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(2)
                result = sock.connect_ex(('127.0.0.1', 5001))
                if result == 0:
                    print("Django服务器启动成功")
                    server_running = True
                else:
                    print("无法连接到Django服务器")
                    server_running = False
        except Exception as e:
            print(f"检查服务器连接失败: {e}")
            server_running = False
        
        # 停止服务器
        try:
            process.terminate()
            process.wait(timeout=3)
            print("测试服务器已停止")
        except subprocess.TimeoutExpired:
            process.kill()
            print("强制停止测试服务器")
        
        return server_running
        
    except Exception as e:
        print(f"服务器测试失败: {e}")
        return False

def main():
    """主函数"""
    print("Django简化修复脚本")
    print("=" * 40)
    
    success_count = 0
    total_steps = 3
    
    # 步骤1: 修复数据库迁移
    if fix_database_migration():
        success_count += 1
        print("步骤1: 数据库修复 - 成功")
    else:
        print("步骤1: 数据库修复 - 失败")
    
    # 步骤2: 创建静态文件
    if create_static_dirs():
        success_count += 1
        print("步骤2: 静态文件创建 - 成功")
    else:
        print("步骤2: 静态文件创建 - 失败")
    
    # 步骤3: 测试服务器
    if test_django_server():
        success_count += 1
        print("步骤3: 服务器测试 - 成功")
    else:
        print("步骤3: 服务器测试 - 失败")
    
    print("\n" + "=" * 40)
    print(f"修复结果: {success_count}/{total_steps} 步骤成功")
    
    if success_count == total_steps:
        print("Django修复完成！")
        print("\n下一步:")
        print("1. 启动Django服务器:")
        print("   python manage.py runserver 127.0.0.1:5001")
        print("2. 在浏览器中访问:")
        print("   http://127.0.0.1:5001/")
        print("3. 测试RunSim GUI集成")
        return True
    else:
        print("部分修复失败，请检查错误信息")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"修复过程中发生错误: {e}")
        sys.exit(1)
