#!/usr/bin/env python
"""
简单的Django测试脚本
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

def test_django():
    """测试Django"""
    print("测试Django导入...")
    try:
        import django
        print(f"✅ Django版本: {django.VERSION}")
        return True
    except ImportError as e:
        print(f"❌ Django导入失败: {e}")
        return False

def test_settings():
    """测试Django设置"""
    print("测试Django设置...")
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dashboard_project.settings')
        import django
        django.setup()
        print("✅ Django设置成功")
        return True
    except Exception as e:
        print(f"❌ Django设置失败: {e}")
        return False

def test_models():
    """测试模型导入"""
    print("测试模型导入...")
    try:
        from dashboard_app.models import Project, TestCase, Bug
        print("✅ 模型导入成功")
        return True
    except Exception as e:
        print(f"❌ 模型导入失败: {e}")
        return False

def test_database():
    """测试数据库"""
    print("测试数据库连接...")
    try:
        from django.db import connection
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        print("✅ 数据库连接成功")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def main():
    """主函数"""
    print("Django简单测试")
    print("=" * 40)
    
    tests = [
        test_django,
        test_settings,
        test_models,
        test_database,
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
