#!/usr/bin/env python
"""
Django迁移完整修复脚本

解决所有已知问题并完成迁移
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

def run_script(script_name, description):
    """运行Python脚本"""
    print(f"\n{'='*50}")
    print(f"执行: {description}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(
            [sys.executable, script_name],
            cwd=current_dir,
            capture_output=True,
            text=True,
            timeout=120
        )
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            return True
        else:
            print(f"❌ {description} 失败 (返回码: {result.returncode})")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} 执行超时")
        return False
    except Exception as e:
        print(f"❌ {description} 执行异常: {e}")
        return False

def run_django_command(command, description):
    """运行Django管理命令"""
    print(f"\n执行Django命令: {description}")
    print(f"命令: python manage.py {command}")
    
    try:
        result = subprocess.run(
            [sys.executable, 'manage.py'] + command.split(),
            cwd=current_dir,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            return True
        else:
            print(f"❌ {description} 失败 (返回码: {result.returncode})")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} 执行超时")
        return False
    except Exception as e:
        print(f"❌ {description} 执行异常: {e}")
        return False

def check_django():
    """检查Django环境"""
    print("检查Django环境...")
    try:
        import django
        print(f"✅ Django版本: {django.VERSION}")
        return True
    except ImportError:
        print("❌ Django未安装")
        return False

def main():
    """主函数"""
    print("🚀 Django迁移完整修复")
    print("=" * 60)
    
    # 检查Django环境
    if not check_django():
        print("请先安装Django: pip install Django>=4.2.0")
        return False
    
    # 修复步骤
    steps = [
        ("fix_templates.py", "修复模板语法"),
        ("fix_migration.py", "修复数据库迁移冲突"),
    ]
    
    success_count = 0
    for script, description in steps:
        if run_script(script, description):
            success_count += 1
        else:
            print(f"⚠️ {description} 失败，但继续执行后续步骤")
    
    # Django命令
    django_commands = [
        ("check", "Django系统检查"),
        ("collectstatic --noinput", "收集静态文件"),
    ]
    
    for command, description in django_commands:
        if run_django_command(command, description):
            success_count += 1
    
    # 测试服务器启动
    print(f"\n{'='*50}")
    print("测试Django服务器启动")
    print(f"{'='*50}")
    
    print("启动Django开发服务器进行测试...")
    try:
        # 启动服务器进程
        process = subprocess.Popen(
            [sys.executable, 'manage.py', 'runserver', '127.0.0.1:5001', '--noreload'],
            cwd=current_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待服务器启动
        import time
        time.sleep(5)
        
        # 检查服务器是否运行
        import socket

# 配置UTF-8输出
import sys
import io
if hasattr(sys.stdout, 'buffer'):
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
if hasattr(sys.stderr, 'buffer'):
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(3)
                result = sock.connect_ex(('127.0.0.1', 5001))
                if result == 0:
                    print("✅ Django服务器启动成功")
                    server_test = True
                else:
                    print("❌ 无法连接到Django服务器")
                    server_test = False
        except Exception as e:
            print(f"❌ 检查服务器连接失败: {e}")
            server_test = False
        
        # 停止服务器
        try:
            process.terminate()
            process.wait(timeout=5)
            print("✅ 测试服务器已停止")
        except subprocess.TimeoutExpired:
            process.kill()
            print("⚠️ 强制停止测试服务器")
        except Exception as e:
            print(f"⚠️ 停止服务器时出错: {e}")
        
    except Exception as e:
        print(f"❌ 服务器测试失败: {e}")
        server_test = False
    
    # 输出结果
    print(f"\n{'='*60}")
    if server_test:
        print("🎉 Django迁移修复完成！")
        print("✅ 所有问题已解决，Django服务器可以正常启动")
    else:
        print("⚠️ Django迁移基本完成，但服务器测试失败")
        print("请手动检查错误信息")
    print(f"{'='*60}")
    
    print(f"\n📋 下一步操作:")
    print("1. 启动Django服务器:")
    print("   python manage.py runserver 127.0.0.1:5001")
    print("")
    print("2. 在浏览器中访问:")
    print("   http://127.0.0.1:5001/")
    print("")
    print("3. 测试RunSim GUI集成:")
    print("   - 启动RunSim GUI")
    print("   - 点击工具菜单中的'项目仪表板'")
    print("   - 验证所有功能正常")
    print("")
    
    if server_test:
        print("🎊 恭喜！Django迁移成功完成！")
    
    return server_test

if __name__ == '__main__':
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
