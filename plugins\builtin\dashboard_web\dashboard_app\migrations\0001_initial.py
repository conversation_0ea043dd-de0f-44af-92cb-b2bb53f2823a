# Generated by Django 5.2.2 on 2025-06-05 03:21

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='项目名称')),
                ('subsystem', models.CharField(blank=True, max_length=255, null=True, verbose_name='子系统')),
                ('description', models.TextField(blank=True, null=True, verbose_name='项目描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '项目',
                'verbose_name_plural': '项目',
                'db_table': 'projects',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SystemConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('config_key', models.CharField(max_length=255, unique=True, verbose_name='配置键')),
                ('config_value', models.TextField(blank=True, null=True, verbose_name='配置值')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '系统配置',
                'verbose_name_plural': '系统配置',
                'db_table': 'system_config',
                'ordering': ['config_key'],
            },
        ),
        migrations.CreateModel(
            name='TestCase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category', models.CharField(blank=True, max_length=255, null=True, verbose_name='分类')),
                ('number', models.CharField(blank=True, max_length=255, null=True, verbose_name='编号')),
                ('test_areas', models.CharField(blank=True, max_length=255, null=True, verbose_name='测试区域')),
                ('test_scope', models.CharField(blank=True, max_length=255, null=True, verbose_name='测试范围')),
                ('function_point', models.CharField(blank=True, max_length=255, null=True, verbose_name='功能点')),
                ('check_point', models.CharField(blank=True, max_length=255, null=True, verbose_name='检查点')),
                ('cover', models.CharField(blank=True, max_length=255, null=True, verbose_name='覆盖')),
                ('case_name', models.CharField(max_length=255, verbose_name='用例名称')),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('actual_time', models.IntegerField(blank=True, null=True, verbose_name='实际耗时(秒)')),
                ('owner', models.CharField(blank=True, max_length=255, null=True, verbose_name='负责人')),
                ('subsys_phase', models.CharField(blank=True, choices=[('DVR1', 'DVR1'), ('DVR2', 'DVR2'), ('DVR3', 'DVR3'), ('DVS1', 'DVS1'), ('DVS2', 'DVS2'), ('N/A', 'N/A')], max_length=50, null=True, verbose_name='子系统阶段')),
                ('subsys_status', models.CharField(choices=[('PASS', 'PASS'), ('Pending', 'Pending'), ('On-Going', 'On-Going'), ('N/A', 'N/A')], default='Pending', max_length=50, verbose_name='子系统状态')),
                ('top_phase', models.CharField(blank=True, choices=[('DVR1', 'DVR1'), ('DVR2', 'DVR2'), ('DVR3', 'DVR3'), ('DVS1', 'DVS1'), ('DVS2', 'DVS2'), ('N/A', 'N/A')], max_length=50, null=True, verbose_name='TOP阶段')),
                ('top_status', models.CharField(choices=[('PASS', 'PASS'), ('Pending', 'Pending'), ('On-Going', 'On-Going'), ('N/A', 'N/A')], default='Pending', max_length=50, verbose_name='TOP状态')),
                ('post_subsys_phase', models.CharField(blank=True, max_length=50, null=True, verbose_name='后仿子系统阶段')),
                ('post_subsys_status', models.CharField(choices=[('PASS', 'PASS'), ('Pending', 'Pending'), ('On-Going', 'On-Going'), ('N/A', 'N/A')], default='Pending', max_length=50, verbose_name='后仿子系统状态')),
                ('post_top_phase', models.CharField(blank=True, max_length=50, null=True, verbose_name='后仿TOP阶段')),
                ('post_top_status', models.CharField(choices=[('PASS', 'PASS'), ('Pending', 'Pending'), ('On-Going', 'On-Going'), ('N/A', 'N/A')], default='Pending', max_length=50, verbose_name='后仿TOP状态')),
                ('remarks', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('subsys_stage', models.CharField(blank=True, max_length=50, null=True, verbose_name='子系统阶段(旧)')),
                ('top_stage', models.CharField(blank=True, max_length=50, null=True, verbose_name='TOP阶段(旧)')),
                ('test_process', models.CharField(blank=True, max_length=255, null=True, verbose_name='测试流程')),
                ('coverage_point', models.CharField(blank=True, max_length=255, null=True, verbose_name='覆盖点')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard_app.project', verbose_name='项目')),
            ],
            options={
                'verbose_name': '测试用例',
                'verbose_name_plural': '测试用例',
                'db_table': 'test_cases',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CaseStatusHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('old_status', models.CharField(blank=True, max_length=50, null=True, verbose_name='旧状态')),
                ('new_status', models.CharField(blank=True, max_length=50, null=True, verbose_name='新状态')),
                ('stage_type', models.CharField(choices=[('subsys', 'subsys'), ('top', 'top'), ('post_subsys', 'post_subsys'), ('post_top', 'post_top')], max_length=50, verbose_name='阶段类型')),
                ('changed_by', models.CharField(blank=True, max_length=255, null=True, verbose_name='修改人')),
                ('changed_at', models.DateTimeField(auto_now_add=True, verbose_name='修改时间')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard_app.testcase', verbose_name='测试用例')),
            ],
            options={
                'verbose_name': '用例状态历史',
                'verbose_name_plural': '用例状态历史',
                'db_table': 'case_status_history',
                'ordering': ['-changed_at'],
            },
        ),
        migrations.CreateModel(
            name='PhaseStatistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phase_name', models.CharField(choices=[('DVR1', 'DVR1'), ('DVR2', 'DVR2'), ('DVR3', 'DVR3'), ('DVS1', 'DVS1'), ('DVS2', 'DVS2')], max_length=50, verbose_name='阶段名称')),
                ('case_type', models.CharField(choices=[('subsys', 'subsys'), ('top', 'top'), ('post_subsys', 'post_subsys'), ('post_top', 'post_top')], max_length=50, verbose_name='用例类型')),
                ('total_cases', models.IntegerField(default=0, verbose_name='总用例数')),
                ('pass_cases', models.IntegerField(default=0, verbose_name='通过用例数')),
                ('fail_cases', models.IntegerField(default=0, verbose_name='失败用例数')),
                ('ongoing_cases', models.IntegerField(default=0, verbose_name='进行中用例数')),
                ('not_started_cases', models.IntegerField(default=0, verbose_name='未开始用例数')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard_app.project', verbose_name='项目')),
            ],
            options={
                'verbose_name': '阶段统计',
                'verbose_name_plural': '阶段统计',
                'db_table': 'phase_statistics',
                'ordering': ['project', 'phase_name', 'case_type'],
                'unique_together': {('project', 'phase_name', 'case_type')},
            },
        ),
        migrations.CreateModel(
            name='Bug',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bug_id', models.CharField(max_length=255, verbose_name='BUG ID')),
                ('bug_type', models.CharField(blank=True, max_length=255, null=True, verbose_name='BUG类型')),
                ('submit_sys', models.CharField(blank=True, max_length=255, null=True, verbose_name='提交系统')),
                ('verification_stage', models.CharField(blank=True, max_length=255, null=True, verbose_name='验证阶段')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述')),
                ('discovery_platform', models.CharField(blank=True, max_length=255, null=True, verbose_name='发现平台')),
                ('discovery_case', models.CharField(blank=True, max_length=255, null=True, verbose_name='发现用例')),
                ('severity', models.CharField(choices=[('Low', 'Low'), ('Medium', 'Medium'), ('High', 'High'), ('Critical', 'Critical')], default='Medium', max_length=50, verbose_name='严重程度')),
                ('status', models.CharField(choices=[('Open', 'Open'), ('In Progress', 'In Progress'), ('Fixed', 'Fixed'), ('Closed', 'Closed'), ('Rejected', 'Rejected')], default='Open', max_length=50, verbose_name='状态')),
                ('submitter', models.CharField(blank=True, max_length=255, null=True, verbose_name='提交人')),
                ('verifier', models.CharField(blank=True, max_length=255, null=True, verbose_name='验证人')),
                ('submit_date', models.DateField(blank=True, null=True, verbose_name='提交日期')),
                ('fix_date', models.DateField(blank=True, null=True, verbose_name='修复日期')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard_app.project', verbose_name='项目')),
            ],
            options={
                'verbose_name': 'BUG',
                'verbose_name_plural': 'BUG',
                'db_table': 'bugs',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['project'], name='bugs_project_b3170b_idx'), models.Index(fields=['status'], name='bugs_status_420e82_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='testcase',
            index=models.Index(fields=['case_name'], name='test_cases_case_na_7e3424_idx'),
        ),
        migrations.AddIndex(
            model_name='testcase',
            index=models.Index(fields=['project'], name='test_cases_project_57edee_idx'),
        ),
        migrations.AddIndex(
            model_name='casestatushistory',
            index=models.Index(fields=['case'], name='case_status_case_id_67eee6_idx'),
        ),
    ]
