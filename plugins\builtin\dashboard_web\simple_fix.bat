@echo off
chcp 65001 >nul
echo ========================================
echo Django 简化修复脚本
echo ========================================
echo.

cd /d "%~dp0"
echo 当前目录: %CD%
echo.

echo 执行Django修复...
python simple_fix.py

if errorlevel 1 (
    echo.
    echo 修复过程中遇到问题，但可能部分功能已修复
    echo 请尝试手动启动服务器测试
) else (
    echo.
    echo 修复完成！
)

echo.
echo 启动Django服务器测试:
echo python manage.py runserver 127.0.0.1:5001
echo.
pause
