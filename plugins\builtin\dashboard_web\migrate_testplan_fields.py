#!/usr/bin/env python3
"""
TestPlan字段迁移脚本

该脚本用于将现有数据库的test_cases表更新为新的字段结构，
以支持修改后的TestPlan模板格式。

新字段映射：
- C列: Test Scope -> Test Areas
- E列: Test Process -> Test Scope  
- G列: Coverage Point -> Cover
- H列: Case Name -> TestCase Name
"""

import os
import sys
import sqlite3
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_column_exists(cursor, table_name, column_name):
    """检查列是否存在"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [row[1] for row in cursor.fetchall()]
    return column_name in columns

def migrate_database(db_path):
    """迁移数据库结构"""
    try:
        logger.info(f"开始迁移数据库: {db_path}")
        
        if not os.path.exists(db_path):
            logger.error(f"数据库文件不存在: {db_path}")
            return False
        
        # 备份数据库
        backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        import shutil

# 配置UTF-8输出
import sys
import io
if hasattr(sys.stdout, 'buffer'):
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
if hasattr(sys.stderr, 'buffer'):
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

        shutil.copy2(db_path, backup_path)
        logger.info(f"数据库已备份到: {backup_path}")
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查test_cases表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_cases'")
            if not cursor.fetchone():
                logger.error("test_cases表不存在")
                return False
            
            # 检查并添加新字段
            new_columns = [
                ('test_areas', 'TEXT'),
                ('cover', 'TEXT')
            ]
            
            for column_name, column_type in new_columns:
                if not check_column_exists(cursor, 'test_cases', column_name):
                    logger.info(f"添加新列: {column_name}")
                    cursor.execute(f"ALTER TABLE test_cases ADD COLUMN {column_name} {column_type}")
                else:
                    logger.info(f"列已存在: {column_name}")
            
            # 检查兼容性字段
            compat_columns = [
                ('test_process', 'TEXT'),
                ('coverage_point', 'TEXT')
            ]
            
            for column_name, column_type in compat_columns:
                if not check_column_exists(cursor, 'test_cases', column_name):
                    logger.info(f"添加兼容性列: {column_name}")
                    cursor.execute(f"ALTER TABLE test_cases ADD COLUMN {column_name} {column_type}")
                else:
                    logger.info(f"兼容性列已存在: {column_name}")
            
            # 数据迁移：将旧字段数据复制到新字段
            logger.info("开始数据迁移...")
            
            # 如果test_areas为空，尝试从test_scope复制数据
            cursor.execute("""
                UPDATE test_cases 
                SET test_areas = test_scope 
                WHERE test_areas IS NULL OR test_areas = ''
            """)
            
            # 如果cover为空，尝试从coverage_point复制数据
            cursor.execute("""
                UPDATE test_cases 
                SET cover = coverage_point 
                WHERE cover IS NULL OR cover = ''
            """)
            
            conn.commit()
            logger.info("数据迁移完成")
            
            # 验证迁移结果
            cursor.execute("SELECT COUNT(*) FROM test_cases")
            total_cases = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM test_cases WHERE test_areas IS NOT NULL AND test_areas != ''")
            cases_with_test_areas = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM test_cases WHERE cover IS NOT NULL AND cover != ''")
            cases_with_cover = cursor.fetchone()[0]
            
            logger.info(f"迁移验证:")
            logger.info(f"  总用例数: {total_cases}")
            logger.info(f"  有test_areas数据的用例: {cases_with_test_areas}")
            logger.info(f"  有cover数据的用例: {cases_with_cover}")
            
            return True
            
    except Exception as e:
        logger.error(f"数据库迁移失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("TestPlan字段迁移工具")
    print("=" * 50)
    
    # 获取数据库路径
    dashboard_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(dashboard_dir, 'data', 'dashboard.db')
    
    if len(sys.argv) > 1:
        db_path = sys.argv[1]
    
    print(f"数据库路径: {db_path}")
    
    # 确认迁移
    response = input("是否继续迁移？(y/N): ").strip().lower()
    if response != 'y':
        print("迁移已取消")
        return
    
    # 执行迁移
    success = migrate_database(db_path)
    
    if success:
        print("\n✅ 数据库迁移成功！")
        print("\n新字段说明:")
        print("- test_areas: 测试区域 (对应Excel C列)")
        print("- cover: 覆盖 (对应Excel G列)")
        print("- 保留了旧字段以确保兼容性")
        print("\n现在可以使用新的TestPlan模板格式了！")
    else:
        print("\n❌ 数据库迁移失败！")
        print("请检查日志信息并联系技术支持")

if __name__ == "__main__":
    main()
