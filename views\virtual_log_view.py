"""
虚拟滚动日志视图组件（第二阶段优化版本 + 文本选择功能）

新增功能：
1. 鼠标文本选择（单击拖拽、双击选词、三击选行）
2. 键盘快捷键支持（Ctrl+C复制、Ctrl+A全选、ESC清除选择）
3. 右键菜单（复制、全选、清除选择）
4. 选择区域高亮显示
5. 剪贴板集成
"""
from PyQt5.QtWidgets import (
    QAbstractScrollArea, QStyleOption, QStyle,
    QApplication, QScrollBar, QMenu, QAction
)
from PyQt5.QtCore import Qt, QRect, QRectF, QSize, pyqtSignal, QTimer, QPoint
from PyQt5.QtGui import (
    QPainter, QColor, QTextOption, QFont, QFontMetrics,
    QClipboard, QKeySequence, QCursor
)
import time
import uuid
import threading
from collections import OrderedDict

class VirtualLogView(QAbstractScrollArea):
    """
    虚拟滚动日志视图，只渲染可见部分的日志行

    优化点：
    1. 只渲染可见区域的日志行，大幅减少渲染开销
    2. 使用行列表存储日志，避免大字符串拼接和处理
    3. 实现高效的行缓存机制，减少重复渲染
    4. 支持高效的日志追加和滚动操作
    """

    # 定义信号
    scrolled_to_bottom = pyqtSignal()  # 滚动到底部信号

    # 常量定义
    MAX_LINES = 100000  # 最大行数
    RENDER_MARGIN = 5   # 额外渲染的行数（上下各多渲染几行，避免滚动时出现空白）

    def __init__(self, parent=None):
        """初始化虚拟滚动日志视图（第二阶段优化版本 + 文本选择功能）"""
        super().__init__(parent)

        # 日志数据
        self._lines = []  # 存储所有日志行
        self._line_heights = []  # 每行的高度
        self._total_height = 0  # 所有行的总高度

        # 渲染相关
        self._line_spacing = 2  # 行间距
        self._font = QFont("Consolas", 9)  # 默认字体
        self._font_metrics = QFontMetrics(self._font)
        self._default_line_height = self._font_metrics.height() + self._line_spacing

        # 颜色和样式
        self._background_color = QColor(255, 255, 255)  # 背景色
        self._text_color = QColor(51, 51, 51)  # 文本颜色
        self._selection_color = QColor(173, 214, 255)  # 选择颜色
        self._selection_text_color = QColor(0, 0, 0)  # 选中文本颜色

        # 文本选择相关
        self._selection_start = None  # 选择开始位置 (line, char)
        self._selection_end = None    # 选择结束位置 (line, char)
        self._is_selecting = False    # 是否正在选择
        self._last_click_time = 0     # 上次点击时间
        self._click_count = 0         # 连续点击次数
        self._double_click_threshold = 500  # 双击时间阈值（毫秒）

        # 滚动相关
        self._auto_scroll = True  # 自动滚动标志
        self._scroll_timer = QTimer()
        self._scroll_timer.timeout.connect(self._check_auto_scroll)
        self._scroll_timer.start(100)  # 每100ms检查一次是否需要自动滚动

        # 第二阶段优化：缓存机制
        self._render_cache = OrderedDict()  # 渲染缓存
        self._cache_max_size = 50  # 最大缓存项数
        self._cache_lock = threading.Lock()  # 缓存锁

        # 第二阶段优化：批处理
        self._pending_lines = []  # 待处理的行
        self._batch_timer = QTimer()
        self._batch_timer.timeout.connect(self._process_batch)
        self._batch_timer.setSingleShot(True)
        self._batch_size = 100  # 批处理大小
        self._batch_timeout = 50  # 批处理超时（毫秒）

        # 第二阶段优化：性能统计
        self._render_stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'total_renders': 0,
            'avg_render_time': 0.0
        }

        # 初始化滚动条
        self.verticalScrollBar().setRange(0, 0)
        self.verticalScrollBar().valueChanged.connect(self._handle_scroll)

        # 设置视口属性
        self.viewport().setAutoFillBackground(False)

        # 设置控件属性
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setMinimumHeight(100)

        # 启用鼠标跟踪和焦点
        self.setMouseTracking(True)
        self.setFocusPolicy(Qt.StrongFocus)

        # 创建右键菜单
        self._create_context_menu()

    def append(self, text):
        """
        追加文本到日志视图（第二阶段优化：使用批处理）

        Args:
            text (str): 要追加的文本
        """
        # 如果文本为空，直接返回
        if not text:
            return

        try:
            # 分割多行文本
            new_lines = text.split('\n')

            # 过滤掉空行，避免不必要的处理
            new_lines = [line for line in new_lines if line.strip()]

            # 如果没有有效行，直接返回
            if not new_lines:
                return

            # 添加到待处理队列
            self._pending_lines.extend(new_lines)

            # 如果达到批处理大小或者是第一批数据，立即处理
            if len(self._pending_lines) >= self._batch_size or not self._lines:
                self._process_batch()
            else:
                # 否则启动批处理定时器
                if not self._batch_timer.isActive():
                    self._batch_timer.start(self._batch_timeout)

        except Exception as e:
            print(f"追加日志时出错: {str(e)}")

    def _process_batch(self):
        """处理批量日志数据"""
        if not self._pending_lines:
            return

        # 停止批处理定时器
        self._batch_timer.stop()

        # 记录开始时间（用于性能监控）
        start_time = time.time()

        try:
            # 获取待处理的行
            lines_to_process = self._pending_lines[:]
            self._pending_lines.clear()

            line_count = len(lines_to_process)

            # 批量处理行，而不是逐行处理
            # 预先计算所有行高
            line_heights = [self._default_line_height] * line_count  # 使用默认行高
            total_height_to_add = sum(line_heights)

            # 批量添加到列表
            self._lines.extend(lines_to_process)
            self._line_heights.extend(line_heights)
            self._total_height += total_height_to_add

            # 如果超过最大行数，移除旧行
            if len(self._lines) > self.MAX_LINES:
                lines_to_remove = len(self._lines) - self.MAX_LINES
                height_to_remove = sum(self._line_heights[:lines_to_remove])

                self._lines = self._lines[lines_to_remove:]
                self._line_heights = self._line_heights[lines_to_remove:]
                self._total_height -= height_to_remove

                # 清空缓存，因为行索引已经改变
                self._clear_render_cache()

            # 更新滚动条范围
            self._update_scrollbar()

            # 检查是否在底部
            was_at_bottom = self.verticalScrollBar().value() >= self.verticalScrollBar().maximum() - 10

            # 如果之前在底部或者自动滚动已启用，则滚动到底部
            if was_at_bottom or self._auto_scroll:
                self._auto_scroll = True
                # 延迟滚动到底部，避免频繁更新
                QTimer.singleShot(0, self._scroll_to_bottom)

            # 更新视图 - 使用延迟更新减少重绘次数
            self.viewport().update()

            # 记录处理时间（用于性能监控）
            process_time = (time.time() - start_time) * 1000.0  # 转换为毫秒

            # 如果处理时间过长，输出警告
            if process_time > 100.0:  # 提高阈值到100ms
                print(f"警告: 虚拟日志视图批处理时间过长: {process_time:.1f}ms, 行数: {line_count}")

            # 使用弱引用访问性能监控器，避免引用已删除对象
            try:
                parent = self.parent()
                if parent and hasattr(parent, 'performance_monitor') and parent.performance_monitor:
                    parent.performance_monitor.record_log_processing(line_count)
            except Exception:
                # 忽略性能监控器可能已被删除的错误
                pass

        except Exception as e:
            print(f"批处理日志时出错: {str(e)}")

    def clear(self):
        """清空日志视图"""
        self._lines = []
        self._line_heights = []
        self._total_height = 0
        self._pending_lines.clear()
        self._clear_render_cache()
        self._update_scrollbar()
        self.viewport().update()

    def _clear_render_cache(self):
        """清空渲染缓存"""
        with self._cache_lock:
            self._render_cache.clear()

    def _get_cache_key(self, start_line, end_line, scroll_value):
        """生成缓存键"""
        return f"{start_line}_{end_line}_{scroll_value}"

    def _get_cached_render_data(self, cache_key):
        """获取缓存的渲染数据"""
        with self._cache_lock:
            if cache_key in self._render_cache:
                # 移动到末尾（LRU）
                self._render_cache.move_to_end(cache_key)
                self._render_stats['cache_hits'] += 1
                return self._render_cache[cache_key]

            self._render_stats['cache_misses'] += 1
            return None

    def _cache_render_data(self, cache_key, render_data):
        """缓存渲染数据"""
        with self._cache_lock:
            # 如果缓存已满，移除最旧的项
            if len(self._render_cache) >= self._cache_max_size:
                self._render_cache.popitem(last=False)

            self._render_cache[cache_key] = render_data

    def get_cache_stats(self):
        """获取缓存统计信息"""
        total_requests = self._render_stats['cache_hits'] + self._render_stats['cache_misses']
        hit_rate = (self._render_stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0

        return {
            'cache_size': len(self._render_cache),
            'cache_hits': self._render_stats['cache_hits'],
            'cache_misses': self._render_stats['cache_misses'],
            'hit_rate': hit_rate,
            'total_renders': self._render_stats['total_renders'],
            'avg_render_time': self._render_stats['avg_render_time']
        }

    def setText(self, text):
        """
        设置日志视图的文本内容

        Args:
            text (str): 要设置的文本
        """
        self.clear()
        self.append(text)

    def text(self):
        """
        获取日志视图的文本内容

        Returns:
            str: 日志视图的文本内容
        """
        return '\n'.join(self._lines)

    def setFont(self, font):
        """
        设置日志视图的字体

        Args:
            font (QFont): 要设置的字体
        """
        self._font = font
        self._font_metrics = QFontMetrics(self._font)
        self._default_line_height = self._font_metrics.height() + self._line_spacing

        # 重新计算所有行的高度
        self._recalculate_line_heights()
        self._update_scrollbar()
        self.viewport().update()

    def setAutoScroll(self, enabled):
        """
        设置是否启用自动滚动

        Args:
            enabled (bool): 是否启用自动滚动
        """
        self._auto_scroll = enabled
        if enabled:
            self._scroll_to_bottom()

    def paintEvent(self, event):
        """
        绘制事件处理（第二阶段优化：使用缓存）

        Args:
            event (QPaintEvent): 绘制事件
        """
        try:
            # 记录渲染开始时间（用于性能监控）
            render_start_time = time.time()
            self._render_stats['total_renders'] += 1

            painter = QPainter(self.viewport())
            painter.setFont(self._font)

            # 绘制背景
            painter.fillRect(event.rect(), self._background_color)

            # 获取可见区域
            visible_rect = self.viewport().rect()
            scroll_value = self.verticalScrollBar().value()

            # 计算可见行的范围
            visible_lines = self._get_visible_lines(scroll_value, visible_rect.height())
            if not visible_lines:
                painter.end()
                return

            start_line, end_line, y_offset = visible_lines

            # 生成缓存键
            cache_key = self._get_cache_key(start_line, end_line, scroll_value // 10)  # 降低缓存精度

            # 尝试从缓存获取渲染数据
            cached_data = self._get_cached_render_data(cache_key)

            if cached_data and len(self._lines) == cached_data['total_lines']:
                # 尝试使用缓存数据快速渲染
                cache_used = self._render_from_cache(painter, cached_data, visible_rect)
                if not cache_used:
                    # 缓存渲染失败（如有选择），重新渲染
                    render_data = self._render_and_cache(painter, start_line, end_line, y_offset, visible_rect, cache_key)
            else:
                # 重新渲染并缓存
                render_data = self._render_and_cache(painter, start_line, end_line, y_offset, visible_rect, cache_key)

            # 确保绘制器正确结束
            painter.end()

            # 记录渲染结束时间（用于性能监控）
            render_time = (time.time() - render_start_time) * 1000.0  # 转换为毫秒

            # 更新平均渲染时间
            self._render_stats['avg_render_time'] = (
                (self._render_stats['avg_render_time'] * (self._render_stats['total_renders'] - 1) + render_time) /
                self._render_stats['total_renders']
            )

            # 如果渲染时间过长，输出警告
            if render_time > 50.0:  # 50ms
                print(f"警告: 虚拟日志视图渲染时间过长: {render_time:.1f}ms, 行数: {end_line - start_line}")

        except Exception as e:
            print(f"绘制日志视图时出错: {str(e)}")
            # 确保在异常情况下也结束绘制器
            if 'painter' in locals() and painter.isActive():
                painter.end()

    def _render_from_cache(self, painter, cached_data, visible_rect):
        """从缓存数据渲染"""
        # 注意：缓存渲染时不支持选择高亮，因为选择状态是动态的
        # 如果有选择，应该使用非缓存渲染
        if self._has_selection():
            # 有选择时不使用缓存，重新渲染
            return False

        painter.setPen(self._text_color)

        for line_data in cached_data['lines']:
            text_rect = QRectF(5, line_data['y'], visible_rect.width() - 10, line_data['height'])
            text_option = QTextOption()
            text_option.setWrapMode(QTextOption.WrapAtWordBoundaryOrAnywhere)
            painter.drawText(text_rect, line_data['text'], text_option)

        return True

    def _render_and_cache(self, painter, start_line, end_line, y_offset, visible_rect, cache_key):
        """渲染并缓存数据"""
        render_data = {
            'lines': [],
            'total_lines': len(self._lines)
        }

        y = y_offset

        for i in range(start_line, end_line):
            if i >= len(self._lines):
                break

            line = self._lines[i]
            line_height = self._line_heights[i]

            # 绘制选择背景（如果有选择）
            self._draw_selection_background(painter, i, y, visible_rect.width() - 10, line_height)

            # 绘制文本
            painter.setPen(self._text_color)
            text_rect = QRectF(5, y, visible_rect.width() - 10, line_height)
            text_option = QTextOption()
            text_option.setWrapMode(QTextOption.WrapAtWordBoundaryOrAnywhere)

            # 如果有选择，需要分段绘制文本以显示不同颜色
            if self._has_selection() and self._line_has_selection(i):
                self._draw_text_with_selection(painter, line, i, text_rect, text_option)
            else:
                painter.drawText(text_rect, line, text_option)

            # 缓存行数据
            render_data['lines'].append({
                'text': line,
                'y': y,
                'height': line_height
            })

            y += line_height

        # 缓存渲染数据
        self._cache_render_data(cache_key, render_data)

        return render_data

    def resizeEvent(self, event):
        """
        调整大小事件处理

        Args:
            event (QResizeEvent): 调整大小事件
        """
        super().resizeEvent(event)
        self._update_scrollbar()

        # 如果启用了自动滚动，滚动到底部
        if self._auto_scroll:
            self._scroll_to_bottom()

    def _calculate_line_height(self, line):
        """
        计算行高

        Args:
            line (str): 行文本

        Returns:
            int: 行高（像素）
        """
        # 简单实现：固定行高
        # 可以扩展为根据文本内容计算实际行高（如处理换行）
        return self._default_line_height

    def _recalculate_line_heights(self):
        """重新计算所有行的高度"""
        self._line_heights = []
        self._total_height = 0

        for line in self._lines:
            line_height = self._calculate_line_height(line)
            self._line_heights.append(line_height)
            self._total_height += line_height

    def _update_scrollbar(self):
        """更新滚动条范围"""
        viewport_height = self.viewport().height()

        # 设置滚动条范围
        max_value = max(0, self._total_height - viewport_height)
        self.verticalScrollBar().setRange(0, max_value)
        self.verticalScrollBar().setPageStep(viewport_height)

    def _get_visible_lines(self, scroll_value, viewport_height):
        """
        获取可见行的范围

        Args:
            scroll_value (int): 滚动条位置
            viewport_height (int): 视口高度

        Returns:
            tuple: (起始行索引, 结束行索引, 起始Y偏移)
        """
        if not self._lines:
            return None

        # 查找起始行
        start_line = 0
        y = 0
        for i, height in enumerate(self._line_heights):
            if y + height > scroll_value:
                start_line = i
                break
            y += height

        # 计算起始行的Y偏移
        y_offset = y - scroll_value

        # 查找结束行（加上额外的渲染行数）
        end_line = start_line
        y = y_offset
        while end_line < len(self._lines) and y < viewport_height + self.RENDER_MARGIN * self._default_line_height:
            y += self._line_heights[end_line]
            end_line += 1

        # 加上额外的渲染行数
        end_line = min(len(self._lines), end_line + self.RENDER_MARGIN)

        return (max(0, start_line - self.RENDER_MARGIN), end_line, y_offset)

    def _handle_scroll(self, value):
        """
        处理滚动事件

        Args:
            value (int): 滚动条位置
        """
        # 检查是否滚动到底部
        if value >= self.verticalScrollBar().maximum():
            self._auto_scroll = True
            self.scrolled_to_bottom.emit()
        else:
            self._auto_scroll = False

        # 更新视图
        self.viewport().update()

    def _scroll_to_bottom(self):
        """滚动到底部"""
        # 获取最大值
        max_value = self.verticalScrollBar().maximum()

        # 设置滚动条值
        self.verticalScrollBar().setValue(max_value)

        # 强制更新视图
        self.viewport().update()

        # 确保处理事件，使滚动立即生效
        QApplication.processEvents()

    def _check_auto_scroll(self):
        """检查是否需要自动滚动"""
        try:
            if self._auto_scroll:
                # 直接设置滚动条值，避免递归调用
                max_value = self.verticalScrollBar().maximum()
                current_value = self.verticalScrollBar().value()

                # 只有当当前值不是最大值时才设置，避免不必要的UI更新
                if current_value != max_value:
                    self.verticalScrollBar().setValue(max_value)
                    # 强制更新视图
                    self.viewport().update()
        except Exception as e:
            # 避免异常导致递归错误
            print(f"自动滚动检查出错: {str(e)}")

    def _create_context_menu(self):
        """创建右键菜单"""
        self._context_menu = QMenu(self)

        # 复制动作
        self._copy_action = QAction("复制", self)
        self._copy_action.setShortcut(QKeySequence.Copy)
        self._copy_action.triggered.connect(self._copy_selection)
        self._context_menu.addAction(self._copy_action)

        # 全选动作
        self._select_all_action = QAction("全选", self)
        self._select_all_action.setShortcut(QKeySequence.SelectAll)
        self._select_all_action.triggered.connect(self._select_all)
        self._context_menu.addAction(self._select_all_action)

        # 清除选择动作
        self._clear_selection_action = QAction("清除选择", self)
        self._clear_selection_action.triggered.connect(self._clear_selection)
        self._context_menu.addAction(self._clear_selection_action)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            # 获取点击位置对应的文本位置
            pos = self._get_text_position_from_point(event.pos())
            if pos is not None:
                current_time = time.time() * 1000  # 转换为毫秒

                # 检查是否是多次点击
                if current_time - self._last_click_time < self._double_click_threshold:
                    self._click_count += 1
                else:
                    self._click_count = 1

                self._last_click_time = current_time

                if self._click_count == 1:
                    # 单击：开始选择
                    self._selection_start = pos
                    self._selection_end = pos
                    self._is_selecting = True
                elif self._click_count == 2:
                    # 双击：选择单词
                    self._select_word_at_position(pos)
                elif self._click_count == 3:
                    # 三击：选择整行
                    self._select_line_at_position(pos)

                self.viewport().update()
        elif event.button() == Qt.RightButton:
            # 右键菜单
            self._show_context_menu(event.pos())

        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self._is_selecting and event.buttons() & Qt.LeftButton:
            # 更新选择结束位置
            pos = self._get_text_position_from_point(event.pos())
            if pos is not None:
                self._selection_end = pos
                self.viewport().update()

        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self._is_selecting = False

        super().mouseReleaseEvent(event)

    def keyPressEvent(self, event):
        """键盘按下事件"""
        if event.matches(QKeySequence.Copy):
            # Ctrl+C 复制
            self._copy_selection()
        elif event.matches(QKeySequence.SelectAll):
            # Ctrl+A 全选
            self._select_all()
        elif event.key() == Qt.Key_Escape:
            # ESC 清除选择
            self._clear_selection()
        else:
            super().keyPressEvent(event)

    def contextMenuEvent(self, event):
        """右键菜单事件"""
        self._show_context_menu(event.pos())

    def _show_context_menu(self, pos):
        """显示右键菜单"""
        # 更新菜单项状态
        has_selection = self._has_selection()
        self._copy_action.setEnabled(has_selection)
        self._clear_selection_action.setEnabled(has_selection)

        # 显示菜单
        self._context_menu.exec_(self.mapToGlobal(pos))

    def _get_text_position_from_point(self, point):
        """从屏幕坐标获取文本位置"""
        try:
            scroll_value = self.verticalScrollBar().value()
            viewport_height = self.viewport().height()

            # 获取可见行范围
            visible_lines = self._get_visible_lines(scroll_value, viewport_height)
            if not visible_lines:
                return None

            start_line, end_line, y_offset = visible_lines

            # 计算点击的行
            y = point.y() - y_offset
            current_y = 0

            for i in range(start_line, min(end_line, len(self._lines))):
                line_height = self._line_heights[i]
                if current_y <= y < current_y + line_height:
                    # 找到对应的行，计算字符位置
                    line_text = self._lines[i]
                    char_x = point.x() - 5  # 减去左边距

                    # 使用字体度量计算字符位置
                    char_pos = 0
                    if char_x > 0 and line_text:
                        for j, char in enumerate(line_text):
                            try:
                                char_width = self._font_metrics.width(char)
                                if char_x < char_width / 2:
                                    break
                                char_x -= char_width
                                char_pos = j + 1
                            except Exception:
                                # 如果字符宽度计算失败，使用平均字符宽度
                                avg_char_width = self._font_metrics.averageCharWidth()
                                char_pos = max(0, min(len(line_text), int(char_x / avg_char_width)))
                                break

                    return (i, min(char_pos, len(line_text)))

                current_y += line_height

            return None
        except Exception as e:
            print(f"获取文本位置时出错: {str(e)}")
            return None

    def _select_word_at_position(self, pos):
        """选择指定位置的单词"""
        line_idx, char_idx = pos
        if line_idx >= len(self._lines):
            return

        line_text = self._lines[line_idx]
        if not line_text or char_idx >= len(line_text):
            return

        # 查找单词边界
        start_idx = char_idx
        end_idx = char_idx

        # 向前查找单词开始
        while start_idx > 0 and line_text[start_idx - 1].isalnum():
            start_idx -= 1

        # 向后查找单词结束
        while end_idx < len(line_text) and line_text[end_idx].isalnum():
            end_idx += 1

        self._selection_start = (line_idx, start_idx)
        self._selection_end = (line_idx, end_idx)

    def _select_line_at_position(self, pos):
        """选择指定位置的整行"""
        line_idx, _ = pos
        if line_idx >= len(self._lines):
            return

        self._selection_start = (line_idx, 0)
        self._selection_end = (line_idx, len(self._lines[line_idx]))

    def _select_all(self):
        """全选所有文本"""
        if not self._lines:
            return

        self._selection_start = (0, 0)
        self._selection_end = (len(self._lines) - 1, len(self._lines[-1]))
        self.viewport().update()

    def _clear_selection(self):
        """清除选择"""
        self._selection_start = None
        self._selection_end = None
        self.viewport().update()

    def _has_selection(self):
        """检查是否有选择"""
        return (self._selection_start is not None and
                self._selection_end is not None and
                self._selection_start != self._selection_end)

    def _copy_selection(self):
        """复制选中的文本到剪贴板"""
        if not self._has_selection():
            return

        try:
            selected_text = self._get_selected_text()
            if selected_text:
                clipboard = QApplication.clipboard()
                clipboard.setText(selected_text)
        except Exception as e:
            print(f"复制文本时出错: {str(e)}")

    def _get_selected_text(self):
        """获取选中的文本"""
        if not self._has_selection():
            return ""

        try:
            start_line, start_char = self._selection_start
            end_line, end_char = self._selection_end

            # 确保开始位置在结束位置之前
            if (start_line > end_line or
                (start_line == end_line and start_char > end_char)):
                start_line, start_char, end_line, end_char = end_line, end_char, start_line, start_char

            selected_lines = []

            if start_line == end_line:
                # 同一行内的选择
                if start_line < len(self._lines):
                    line_text = self._lines[start_line]
                    selected_lines.append(line_text[start_char:end_char])
            else:
                # 跨行选择
                for line_idx in range(start_line, min(end_line + 1, len(self._lines))):
                    line_text = self._lines[line_idx]

                    if line_idx == start_line:
                        # 第一行：从开始字符到行尾
                        selected_lines.append(line_text[start_char:])
                    elif line_idx == end_line:
                        # 最后一行：从行首到结束字符
                        selected_lines.append(line_text[:end_char])
                    else:
                        # 中间行：整行
                        selected_lines.append(line_text)

            return '\n'.join(selected_lines)
        except Exception as e:
            print(f"获取选中文本时出错: {str(e)}")
            return ""

    def _line_has_selection(self, line_idx):
        """检查指定行是否有选择"""
        if not self._has_selection():
            return False

        start_line, _ = self._selection_start
        end_line, _ = self._selection_end

        # 确保开始位置在结束位置之前
        if start_line > end_line:
            start_line, end_line = end_line, start_line

        return start_line <= line_idx <= end_line

    def _draw_selection_background(self, painter, line_idx, y, width, height):
        """绘制选择背景"""
        if not self._has_selection() or not self._line_has_selection(line_idx):
            return

        try:
            start_line, start_char = self._selection_start
            end_line, end_char = self._selection_end

            # 确保开始位置在结束位置之前
            if (start_line > end_line or
                (start_line == end_line and start_char > end_char)):
                start_line, start_char, end_line, end_char = end_line, end_char, start_line, start_char

            line_text = self._lines[line_idx] if line_idx < len(self._lines) else ""

            # 计算选择区域的起始和结束位置
            sel_start_x = 5  # 默认左边距
            sel_end_x = 5 + width

            if line_idx == start_line and line_idx == end_line:
                # 同一行内的选择
                try:
                    sel_start_x = 5 + self._font_metrics.width(line_text[:start_char])
                    sel_end_x = 5 + self._font_metrics.width(line_text[:end_char])
                except Exception:
                    # 使用平均字符宽度作为备选方案
                    avg_width = self._font_metrics.averageCharWidth()
                    sel_start_x = 5 + start_char * avg_width
                    sel_end_x = 5 + end_char * avg_width
            elif line_idx == start_line:
                # 选择的第一行
                try:
                    sel_start_x = 5 + self._font_metrics.width(line_text[:start_char])
                except Exception:
                    sel_start_x = 5 + start_char * self._font_metrics.averageCharWidth()
                sel_end_x = 5 + width
            elif line_idx == end_line:
                # 选择的最后一行
                sel_start_x = 5
                try:
                    sel_end_x = 5 + self._font_metrics.width(line_text[:end_char])
                except Exception:
                    sel_end_x = 5 + end_char * self._font_metrics.averageCharWidth()
            # else: 中间行，使用默认的全行选择

            # 绘制选择背景
            selection_rect = QRectF(sel_start_x, y, sel_end_x - sel_start_x, height)
            painter.fillRect(selection_rect, self._selection_color)

        except Exception as e:
            print(f"绘制选择背景时出错: {str(e)}")

    def _draw_text_with_selection(self, painter, line_text, line_idx, text_rect, text_option):
        """绘制带选择高亮的文本"""
        try:
            start_line, start_char = self._selection_start
            end_line, end_char = self._selection_end

            # 确保开始位置在结束位置之前
            if (start_line > end_line or
                (start_line == end_line and start_char > end_char)):
                start_line, start_char, end_line, end_char = end_line, end_char, start_line, start_char

            if line_idx == start_line and line_idx == end_line:
                # 同一行内的选择
                before_text = line_text[:start_char]
                selected_text = line_text[start_char:end_char]
                after_text = line_text[end_char:]

                # 绘制选择前的文本
                if before_text:
                    painter.setPen(self._text_color)
                    try:
                        before_width = self._font_metrics.width(before_text)
                    except Exception:
                        before_width = len(before_text) * self._font_metrics.averageCharWidth()
                    before_rect = QRectF(text_rect.x(), text_rect.y(), before_width, text_rect.height())
                    painter.drawText(before_rect, before_text, text_option)

                # 绘制选中的文本
                if selected_text:
                    painter.setPen(self._selection_text_color)
                    try:
                        before_width = self._font_metrics.width(before_text) if before_text else 0
                        selected_width = self._font_metrics.width(selected_text)
                    except Exception:
                        before_width = len(before_text) * self._font_metrics.averageCharWidth() if before_text else 0
                        selected_width = len(selected_text) * self._font_metrics.averageCharWidth()
                    selected_rect = QRectF(text_rect.x() + before_width, text_rect.y(),
                                         selected_width, text_rect.height())
                    painter.drawText(selected_rect, selected_text, text_option)

                # 绘制选择后的文本
                if after_text:
                    painter.setPen(self._text_color)
                    try:
                        before_width = self._font_metrics.width(before_text) if before_text else 0
                        selected_width = self._font_metrics.width(selected_text) if selected_text else 0
                        after_width = self._font_metrics.width(after_text)
                    except Exception:
                        before_width = len(before_text) * self._font_metrics.averageCharWidth() if before_text else 0
                        selected_width = len(selected_text) * self._font_metrics.averageCharWidth() if selected_text else 0
                        after_width = len(after_text) * self._font_metrics.averageCharWidth()
                    after_rect = QRectF(text_rect.x() + before_width + selected_width, text_rect.y(),
                                      after_width, text_rect.height())
                    painter.drawText(after_rect, after_text, text_option)
            else:
                # 跨行选择，整行都是选中状态
                painter.setPen(self._selection_text_color)
                painter.drawText(text_rect, line_text, text_option)

        except Exception as e:
            print(f"绘制选中文本时出错: {str(e)}")
            # 出错时使用默认绘制
            painter.setPen(self._text_color)
            painter.drawText(text_rect, line_text, text_option)
