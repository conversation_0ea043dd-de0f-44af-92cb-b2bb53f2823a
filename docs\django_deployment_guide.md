# Django 仪表板部署指南

## 1. 环境要求

### 1.1 Python版本
- Python 3.8 或更高版本
- 推荐使用 Python 3.9+

### 1.2 必需的Python包
```bash
# 核心框架
Django>=4.2.0

# 数据库支持（SQLite已内置）
# 如需其他数据库支持：
# psycopg2-binary>=2.9.0  # PostgreSQL
# mysqlclient>=2.1.0      # MySQL

# 其他依赖
pytz>=2023.3
```

## 2. Django安装

### 2.1 检查当前环境
```powershell
# 检查Python版本
python --version

# 检查pip版本
pip --version

# 检查已安装的包
pip list | findstr -i django
```

### 2.2 安装Django
```powershell
# 方法1：使用pip安装（推荐）
pip install Django>=4.2.0

# 方法2：如果内网环境无法直接安装，使用离线安装
# 1. 在有网络的环境下载Django包
pip download Django>=4.2.0 -d django_packages

# 2. 将下载的包复制到内网环境，然后安装
pip install django_packages/*.whl --no-index --find-links django_packages
```

### 2.3 验证安装
```powershell
# 验证Django安装
python -c "import django; print(django.VERSION)"

# 应该输出类似：(4, 2, 0, 'final', 0)
```

## 3. 数据库迁移

### 3.1 生成迁移文件
```powershell
cd plugins/builtin/dashboard_web
python manage.py makemigrations dashboard_app
```

### 3.2 执行迁移
```powershell
# 执行数据库迁移
python manage.py migrate

# 如果需要同步现有数据库结构
python manage.py migrate --run-syncdb
```

### 3.3 创建超级用户（可选）
```powershell
# 创建Django管理员用户
python manage.py createsuperuser
```

## 4. 静态文件配置

### 4.1 收集静态文件
```powershell
# 收集所有静态文件到STATIC_ROOT
python manage.py collectstatic --noinput
```

### 4.2 验证静态文件
确保以下目录存在并包含必要文件：
```
plugins/builtin/dashboard_web/static/
├── css/
│   └── dashboard.css
├── js/
│   ├── dashboard.js
│   └── error-handler.js
└── uploads/
```

## 5. 启动Django服务器

### 5.1 开发环境启动
```powershell
cd plugins/builtin/dashboard_web
python manage.py runserver 127.0.0.1:5001
```

### 5.2 通过插件启动
Django服务器将通过RunSim GUI插件自动启动，无需手动操作。

### 5.3 验证服务器运行
访问以下URL验证服务器正常运行：
- 主页：http://127.0.0.1:5001/
- 健康检查：http://127.0.0.1:5001/health/
- API健康检查：http://127.0.0.1:5001/api/health/

## 6. 数据迁移

### 6.1 从Flask数据库迁移
如果已有Flask版本的数据库，需要进行数据迁移：

```python
# 创建数据迁移脚本 migrate_data.py
import os
import sqlite3
import django
from django.conf import settings

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dashboard_project.settings')
django.setup()

from dashboard_app.models import Project, TestCase, Bug, CaseStatusHistory, SystemConfig

def migrate_flask_data():
    """从Flask数据库迁移数据到Django"""
    # 连接原有数据库
    flask_db_path = 'path/to/flask/dashboard.db'
    conn = sqlite3.connect(flask_db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # 迁移项目数据
    cursor.execute("SELECT * FROM projects")
    for row in cursor.fetchall():
        Project.objects.get_or_create(
            id=row['id'],
            defaults={
                'name': row['name'],
                'subsystem': row['subsystem'],
                'description': row['description'],
            }
        )
    
    # 迁移测试用例数据
    cursor.execute("SELECT * FROM test_cases")
    for row in cursor.fetchall():
        project = Project.objects.get(id=row['project_id'])
        TestCase.objects.get_or_create(
            id=row['id'],
            defaults={
                'project': project,
                'case_name': row['case_name'],
                'test_areas': row.get('test_areas'),
                'test_scope': row.get('test_scope'),
                # ... 其他字段
            }
        )
    
    # 迁移其他数据...
    conn.close()
    print("数据迁移完成")

if __name__ == '__main__':
    migrate_flask_data()
```

### 6.2 执行数据迁移
```powershell
cd plugins/builtin/dashboard_web
python migrate_data.py
```

## 7. 配置文件更新

### 7.1 更新插件配置
需要更新 `plugins/builtin/dashboard_plugin.py` 以支持Django：

```python
# 在插件中添加Django支持
def _run_django_server(self):
    """运行Django服务器"""
    try:
        from .dashboard_web.django_server import create_django_server
        
        self.django_server = create_django_server(self.server_port)
        if self.django_server.start():
            self.server_running = True
            logger.info(f"Django服务器启动成功: {self.django_server.get_url()}")
        else:
            logger.error("Django服务器启动失败")
            
    except Exception as e:
        logger.error(f"启动Django服务器失败: {e}")
```

### 7.2 更新配置文件
确保 `config.py` 中的配置与Django兼容：

```python
# 添加Django特定配置
DJANGO_SETTINGS = {
    'DEBUG': False,
    'ALLOWED_HOSTS': ['127.0.0.1', 'localhost'],
    'SECRET_KEY': 'your-secret-key-here',
}
```

## 8. 测试和验证

### 8.1 功能测试清单
- [ ] 仪表板页面正常加载
- [ ] API接口正常响应
- [ ] 数据库连接正常
- [ ] 静态文件正常加载
- [ ] 用例管理功能正常
- [ ] BUG管理功能正常
- [ ] 数据导入/导出功能正常

### 8.2 性能测试
```powershell
# 测试API响应时间
curl -w "@curl-format.txt" -o /dev/null -s "http://127.0.0.1:5001/api/health/"

# 测试页面加载时间
curl -w "@curl-format.txt" -o /dev/null -s "http://127.0.0.1:5001/"
```

### 8.3 错误排查
常见问题和解决方案：

1. **Django导入错误**
   ```
   ModuleNotFoundError: No module named 'django'
   ```
   解决：确保Django已正确安装

2. **数据库迁移错误**
   ```
   django.db.utils.OperationalError: no such table
   ```
   解决：执行 `python manage.py migrate`

3. **静态文件404错误**
   ```
   GET /static/css/dashboard.css 404
   ```
   解决：执行 `python manage.py collectstatic`

4. **端口占用错误**
   ```
   OSError: [Errno 98] Address already in use
   ```
   解决：更改端口或停止占用端口的进程

## 9. 生产环境部署

### 9.1 安全配置
```python
# settings.py 生产环境配置
DEBUG = False
ALLOWED_HOSTS = ['your-domain.com', '127.0.0.1']
SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY')

# 安全中间件
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'
```

### 9.2 性能优化
```python
# 数据库连接池
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
        'OPTIONS': {
            'timeout': 20,
            'check_same_thread': False,
        }
    }
}

# 缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'dashboard-cache',
        'TIMEOUT': 300,
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
        }
    }
}
```

## 10. 维护和监控

### 10.1 日志监控
检查Django日志文件：
```
plugins/builtin/dashboard_web/logs/django.log
```

### 10.2 数据库备份
```powershell
# 备份SQLite数据库
copy dashboard.db dashboard.db.backup.%date:~0,4%%date:~5,2%%date:~8,2%
```

### 10.3 更新和升级
```powershell
# 更新Django版本
pip install --upgrade Django

# 检查安全更新
python manage.py check --deploy
```

---

**注意**: 本指南假设在Windows环境下部署。Linux环境下的命令可能略有不同。
