#!/usr/bin/env python
"""
Flask到Django数据迁移脚本

将现有的Flask SQLite数据库数据迁移到Django数据库
"""

import os
import sys
import sqlite3
import django
from datetime import datetime
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dashboard_project.settings')
django.setup()

from dashboard_app.models import Project, TestCase, Bug, CaseStatusHistory, SystemConfig, PhaseStatistics


def find_flask_database():
    """查找Flask数据库文件"""
    possible_paths = [
        'data/dashboard.db',
        'dashboard.db',
        'runsim_dashboard.db',
        '../data/dashboard.db',
        '../dashboard.db',
        '../runsim_dashboard.db',
    ]
    
    for path in possible_paths:
        full_path = current_dir / path
        if full_path.exists():
            print(f"找到Flask数据库: {full_path}")
            return str(full_path)
    
    print("未找到Flask数据库文件")
    return None


def migrate_projects(cursor):
    """迁移项目数据"""
    print("迁移项目数据...")
    
    try:
        cursor.execute("SELECT * FROM projects")
        projects = cursor.fetchall()
        
        migrated_count = 0
        for row in projects:
            project, created = Project.objects.get_or_create(
                id=row[0],  # id
                defaults={
                    'name': row[1] or '默认项目',  # name
                    'subsystem': row[2] or 'default',  # subsystem
                    'description': row[3] or '',  # description
                }
            )
            if created:
                migrated_count += 1
                print(f"  创建项目: {project.name}")
        
        print(f"项目迁移完成: {migrated_count} 个新项目")
        
    except sqlite3.OperationalError as e:
        if "no such table: projects" in str(e):
            print("  projects表不存在，创建默认项目")
            Project.objects.get_or_create(
                name='默认项目',
                defaults={
                    'subsystem': 'default',
                    'description': '系统默认项目'
                }
            )
        else:
            raise


def migrate_test_cases(cursor):
    """迁移测试用例数据"""
    print("迁移测试用例数据...")
    
    try:
        cursor.execute("SELECT * FROM test_cases")
        cases = cursor.fetchall()
        
        # 获取列名
        cursor.execute("PRAGMA table_info(test_cases)")
        columns = [col[1] for col in cursor.fetchall()]
        
        migrated_count = 0
        for row in cases:
            # 将行数据转换为字典
            case_data = dict(zip(columns, row))
            
            # 获取或创建项目
            project_id = case_data.get('project_id', 1)
            try:
                project = Project.objects.get(id=project_id)
            except Project.DoesNotExist:
                project = Project.objects.first()
                if not project:
                    project = Project.objects.create(
                        name='默认项目',
                        subsystem='default',
                        description='系统默认项目'
                    )
            
            # 创建测试用例
            test_case, created = TestCase.objects.get_or_create(
                id=case_data['id'],
                defaults={
                    'project': project,
                    'category': case_data.get('category', ''),
                    'number': case_data.get('number', ''),
                    'test_areas': case_data.get('test_areas', ''),
                    'test_scope': case_data.get('test_scope', ''),
                    'function_point': case_data.get('function_point', ''),
                    'check_point': case_data.get('check_point', ''),
                    'cover': case_data.get('cover', ''),
                    'case_name': case_data.get('case_name', ''),
                    'owner': case_data.get('owner', ''),
                    'subsys_phase': case_data.get('subsys_phase', ''),
                    'subsys_status': case_data.get('subsys_status', 'Pending'),
                    'top_phase': case_data.get('top_phase', ''),
                    'top_status': case_data.get('top_status', 'Pending'),
                    'post_subsys_phase': case_data.get('post_subsys_phase', ''),
                    'post_subsys_status': case_data.get('post_subsys_status', 'Pending'),
                    'post_top_phase': case_data.get('post_top_phase', ''),
                    'post_top_status': case_data.get('post_top_status', 'Pending'),
                    'remarks': case_data.get('remarks', ''),
                    'subsys_stage': case_data.get('subsys_stage', ''),
                    'top_stage': case_data.get('top_stage', ''),
                    'test_process': case_data.get('test_process', ''),
                    'coverage_point': case_data.get('coverage_point', ''),
                    'actual_time': case_data.get('actual_time'),
                }
            )
            
            if created:
                migrated_count += 1
                if migrated_count % 100 == 0:
                    print(f"  已迁移 {migrated_count} 个用例...")
        
        print(f"测试用例迁移完成: {migrated_count} 个新用例")
        
    except sqlite3.OperationalError as e:
        if "no such table: test_cases" in str(e):
            print("  test_cases表不存在，跳过")
        else:
            raise


def migrate_bugs(cursor):
    """迁移BUG数据"""
    print("迁移BUG数据...")
    
    try:
        cursor.execute("SELECT * FROM bugs")
        bugs = cursor.fetchall()
        
        # 获取列名
        cursor.execute("PRAGMA table_info(bugs)")
        columns = [col[1] for col in cursor.fetchall()]
        
        migrated_count = 0
        for row in bugs:
            bug_data = dict(zip(columns, row))
            
            # 获取项目
            project_id = bug_data.get('project_id', 1)
            try:
                project = Project.objects.get(id=project_id)
            except Project.DoesNotExist:
                project = Project.objects.first()
            
            # 创建BUG
            bug, created = Bug.objects.get_or_create(
                id=bug_data['id'],
                defaults={
                    'project': project,
                    'bug_id': bug_data.get('bug_id', ''),
                    'bug_type': bug_data.get('bug_type', ''),
                    'submit_sys': bug_data.get('submit_sys', ''),
                    'verification_stage': bug_data.get('verification_stage', ''),
                    'description': bug_data.get('description', ''),
                    'discovery_platform': bug_data.get('discovery_platform', ''),
                    'discovery_case': bug_data.get('discovery_case', ''),
                    'severity': bug_data.get('severity', 'Medium'),
                    'status': bug_data.get('status', 'Open'),
                    'submitter': bug_data.get('submitter', ''),
                    'verifier': bug_data.get('verifier', ''),
                }
            )
            
            if created:
                migrated_count += 1
        
        print(f"BUG迁移完成: {migrated_count} 个新BUG")
        
    except sqlite3.OperationalError as e:
        if "no such table: bugs" in str(e):
            print("  bugs表不存在，跳过")
        else:
            raise


def migrate_system_config(cursor):
    """迁移系统配置数据"""
    print("迁移系统配置数据...")
    
    try:
        cursor.execute("SELECT * FROM system_config")
        configs = cursor.fetchall()
        
        migrated_count = 0
        for row in configs:
            config, created = SystemConfig.objects.get_or_create(
                config_key=row[1],  # config_key
                defaults={
                    'config_value': row[2] or '',  # config_value
                    'description': row[3] or '',  # description
                }
            )
            if created:
                migrated_count += 1
        
        print(f"系统配置迁移完成: {migrated_count} 个新配置")
        
    except sqlite3.OperationalError as e:
        if "no such table: system_config" in str(e):
            print("  system_config表不存在，跳过")
        else:
            raise


def migrate_case_status_history(cursor):
    """迁移用例状态历史数据"""
    print("迁移用例状态历史数据...")
    
    try:
        cursor.execute("SELECT * FROM case_status_history")
        histories = cursor.fetchall()
        
        migrated_count = 0
        for row in histories:
            try:
                case = TestCase.objects.get(id=row[1])  # case_id
                history, created = CaseStatusHistory.objects.get_or_create(
                    id=row[0],  # id
                    defaults={
                        'case': case,
                        'old_status': row[2],  # old_status
                        'new_status': row[3],  # new_status
                        'stage_type': row[4],  # stage_type
                        'changed_by': row[5],  # changed_by
                    }
                )
                if created:
                    migrated_count += 1
            except TestCase.DoesNotExist:
                print(f"  警告: 用例ID {row[1]} 不存在，跳过历史记录")
        
        print(f"状态历史迁移完成: {migrated_count} 个新记录")
        
    except sqlite3.OperationalError as e:
        if "no such table: case_status_history" in str(e):
            print("  case_status_history表不存在，跳过")
        else:
            raise


def main():
    """主迁移函数"""
    print("开始Flask到Django数据迁移...")
    print(f"当前工作目录: {current_dir}")
    
    # 查找Flask数据库
    flask_db_path = find_flask_database()
    if not flask_db_path:
        print("错误: 未找到Flask数据库文件")
        return False
    
    try:
        # 连接Flask数据库
        conn = sqlite3.connect(flask_db_path)
        cursor = conn.cursor()
        
        print(f"成功连接到Flask数据库: {flask_db_path}")
        
        # 执行迁移
        migrate_projects(cursor)
        migrate_test_cases(cursor)
        migrate_bugs(cursor)
        migrate_system_config(cursor)
        migrate_case_status_history(cursor)
        
        # 关闭连接
        conn.close()
        
        print("\n✅ 数据迁移完成!")
        
        # 显示迁移统计
        print("\n迁移统计:")
        print(f"  项目数量: {Project.objects.count()}")
        print(f"  测试用例数量: {TestCase.objects.count()}")
        print(f"  BUG数量: {Bug.objects.count()}")
        print(f"  系统配置数量: {SystemConfig.objects.count()}")
        print(f"  状态历史数量: {CaseStatusHistory.objects.count()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
