@echo off
echo ========================================
echo RunSim GUI Dashboard Django 迁移脚本
echo ========================================
echo.

cd /d "%~dp0"
echo 当前目录: %CD%
echo.

echo 步骤 1: 测试Django环境
echo ----------------------------------------
python test_django.py
if errorlevel 1 (
    echo.
    echo ❌ Django环境测试失败！
    echo 请检查Django是否正确安装
    pause
    exit /b 1
)

echo.
echo 步骤 2: 生成数据库迁移文件
echo ----------------------------------------
python manage.py makemigrations dashboard_app
if errorlevel 1 (
    echo.
    echo ❌ 生成迁移文件失败！
    pause
    exit /b 1
)

echo.
echo 步骤 3: 执行数据库迁移
echo ----------------------------------------
python manage.py migrate
if errorlevel 1 (
    echo.
    echo ❌ 数据库迁移失败！
    pause
    exit /b 1
)

echo.
echo 步骤 4: 迁移Flask数据 (可选)
echo ----------------------------------------
set /p migrate_data="是否迁移现有Flask数据？(y/N): "
if /i "%migrate_data%"=="y" (
    echo 开始迁移Flask数据...
    python manage.py migrate_flask_data
    if errorlevel 1 (
        echo.
        echo ⚠️ 数据迁移失败，但Django环境已准备就绪
    ) else (
        echo ✅ 数据迁移完成
    )
) else (
    echo 跳过数据迁移
)

echo.
echo 步骤 5: 收集静态文件
echo ----------------------------------------
python manage.py collectstatic --noinput
if errorlevel 1 (
    echo.
    echo ⚠️ 静态文件收集失败，但不影响基本功能
)

echo.
echo 步骤 6: 测试Django服务器
echo ----------------------------------------
echo 启动测试服务器 (按 Ctrl+C 停止)...
timeout /t 3 /nobreak > nul
start /b python manage.py runserver 127.0.0.1:5001
timeout /t 5 /nobreak > nul

echo 测试服务器连接...
curl -s http://127.0.0.1:5001/health/ > nul 2>&1
if errorlevel 1 (
    echo ⚠️ 无法连接到测试服务器
    echo 请手动测试: python manage.py runserver 127.0.0.1:5001
) else (
    echo ✅ 服务器连接成功
    echo 访问地址: http://127.0.0.1:5001/
)

echo.
echo ========================================
echo 🎉 Django迁移完成！
echo ========================================
echo.
echo 下一步:
echo 1. 测试所有功能是否正常
echo 2. 更新RunSim GUI插件配置
echo 3. 备份原有Flask文件
echo.
echo 如需启动服务器，运行:
echo python manage.py runserver 127.0.0.1:5001
echo.
pause
